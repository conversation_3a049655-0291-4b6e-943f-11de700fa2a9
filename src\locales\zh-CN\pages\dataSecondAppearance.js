export default {

    dataSecondAppearance: '数据二次上场',
    createSecondAppearanceTask: '创建二次上场任务',
    pleaseSelect: '请选择',

    // constant
    append: '追加',
    overwrite: '覆盖',
    appendAndOverwrite: '追加+覆盖',
    pending: '待上场',
    running: '上场中',
    succeeded: '上场成功',
    failed: '上场失败',
    warn: '异常',
    success: '成功',
    error: '失败',
    selectTableTip: '按需选择需要二次上场的表',
    confirmRuleTip: '确定表上场的加载方式和规则（不写规则默认上场全表）',
    reviewTip: '请仔细核对二次上场内容、加载方式和上场规则。无误请点击"上场"。',
    // createAppearanceTask
    batchUpdateMode: '批量修改上场方式',
    tableName: '表名',
    cluster: '集群',
    sharding: '分片',
    loadMode: '加载方式',
    appearanceRule: '上场规则',
    enterRule: '输入上场规则',
    enterFilterTip: '可对每个表单独设置上场数据的查询条件；不设置规则即表示全表上场。',
    confirmAppearanceContent: '确定上场内容',
    configAppearanceRule: '配置上场规则',
    infoCheck: '信息核对',
    previousStep: '上一步',
    nextStep: '下一步：',
    appearance: '上场',
    cancel: '取消',
    executingAppearance: '执行上场中!',
    appearanceFailed: '执行上场失败!',
    selectAppearanceTableWarning: '请先选择需二次上场的表',
    availableTables: '可选表',
    selectedTables: '已选表',
    searchPlaceholder: '输入表名/集群/分片搜索',
    // secondAppearanceDetail
    appearanceResult: '上场结果',
    appearanceStatusAndResult: '上场状态和结果',
    errorMessage: '错误信息',
    appearanceTime: '上场时间',
    executionStatus: '执行状态',
    endTime: '结束时间',
    viewReason: '查看原因',
    appearanceTableCount: '需上场表数量',
    appearanceHistory: '上场历史',
    latestAppearance: '最近一次上场',
    failedReason: '失败原因',
    createTask: '创建任务',
    noData: '暂无数据'
};
