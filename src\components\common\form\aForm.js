/*
 * @Description: apm-form
 * @Author: <PERSON><PERSON>
 * @Date: 2022-11-28 13:52:31
 * @LastEditTime: 2024-08-26 11:29:57
 * @LastEditors: yingzx38608 <EMAIL>
 */
import './form.less';
import _ from 'lodash';
import { cutZero } from '@/utils/utils';
export default {
    name: 'apm-form',
    props: {
        formItems: {
            type: Array,
            default: () => []
        },
        proposalList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            forms: {},
            queryFieldCondition: 'gt',
            beforeFiledValue: 0,
            unit: '2',
            columns: [
                {
                    title: this.$t('pages.common.period'),
                    key: 'date',
                    width: 110
                },
                {
                    title: this.$t('pages.common.minValue'),
                    key: 'min',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: this.$t('pages.common.averageValue'),
                    key: 'avg',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: this.$t('pages.common.medianValue'),
                    key: 'p50',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: this.$t('pages.common.maxValue'),
                    key: 'max',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                }
            ],
            proposalDict: {
                yesterday: this.$t('pages.common.productDelayYesterday'),
                week: this.$t('pages.common.productDelayLastWeek'),
                month: this.$t('pages.common.productDelayLastMonth')
            }
        };
    },
    computed: {
        // 时延筛选数据建议表格数据
        proposalTableList: function() {
            const list = [];
            this.proposalList.forEach(ele => {
                const multiple = Math.pow(1000, 3 - this.unit);
                const param = {
                    date: this.proposalDict[ele.date]
                };
                ['avg', 'min', 'p50', 'max'].forEach(item => {
                    param[item] = cutZero((ele[item] / multiple || 0).toFixed(3));
                });
                list.push(param);
            });
            return list;
        },
        // 时延筛选数据建议表格title
        proposalTitle: function() {
            const list = ['s', 'ms', 'μs', 'ns'];
            return this.$t('pages.common.latencyDataSuggestion') + ':' + list[this.unit] + ')';
        }
    },
    mounted() {
    },
    methods: {
        init() {
            this.formItems.forEach(ele => {
                this.$set(this.forms, ele.key, _.cloneDeep(ele.value));
            });
        },
        // 回显数据
        echoFormData(param){
            if (!Object.keys(param)?.length) return;
            Object.keys(param).forEach(key => {
                if (key === 'startDate' || key === 'endDate') return;
                if (key === 'unit'){
                    this.unit = param.unit;
                    return;
                }
                if (key === 'queryFieldCondition'){
                    this.queryFieldCondition = param.queryFieldCondition;
                    return;
                }
                if (key === 'beforeFiledValue'){
                    this.beforeFiledValue = param.beforeFiledValue;
                    return;
                }
                this.$set(this.forms, key, param[key]);
            });
        },
        // 重置查询数据
        reset() {
            this.$refs['formValidate'].resetFields();
            this.queryFieldCondition = 'gt';
            this.beforeFiledValue = 0;
            this.unit = '2';
        },
        // 点击查询
        query() {
            let isQuery = false, queryData = {};
            this.$refs['formValidate'].validate(valid => {
                if (valid) {
                    const param = _.find(this.formItems, { type: 'timescreen' }) ? {
                        queryFieldCondition: this.queryFieldCondition,
                        beforeFiledValue: this.beforeFiledValue,
                        unit: this.unit
                    } : {};
                    queryData = { ...this.forms, ...param };
                    isQuery = true;
                }
            });
            return isQuery ? queryData : false;
        },
        // 时延筛选数值框失去焦点
        fieldValueBlur() {
            this.beforeFiledValue = this.beforeFiledValue ? isNaN(this.beforeFiledValue) ? 0 : parseFloat(this.beforeFiledValue) : 0;
        },
        // 查询时延筛选参照数据
        queryProposal(span) {
            span && this.$emit('queryProposal', span);
        },
        // 监听下拉框变化
        handleSelectChange(key, val) {
            this.$emit('handleSelectChange', key, val);
        },
        // 动态渲染表格数据
        dynamicTable(h, param) {
            return h(
                'div',
                {
                    class: 'td-time',
                    on: {
                        click: () => {
                            this.beforeFiledValue = isNaN(param.row[param.column.key]) ? 0 : cutZero(Number(param.row[param.column.key]).toFixed(1));
                        }
                    }
                },
                cutZero(Number(param.row[param.column.key]).toFixed(1))
            );
        },
        // 处理input图标点击
        handleInputIconClick(icon, key) {
            // clearable需要聚焦才展示,可通过图标方式展示无需聚焦也展示清除图标
            icon === 'close' && (this.forms[key] = '');
        }
    },
    render() {
        this.init();
        // 生成输入框
        const generateInput = (item) => {
            return (
                <h-input
                    v-model={this.forms[item.key]}
                    placeholder={item.placeholder || this.$t('common.placeholder.input')}
                    icon={item.icon}
                    clearable={item.clearable}
                    v-on:on-click={() => this.handleInputIconClick(item.icon, item.key)}
                ></h-input>
            );
        };
        // 生成普通下拉框
        const generateSelect = (item) => {
            return (
                <h-select v-model={this.forms[item.key]}
                    placeholder={item.placeholder || this.$t('common.placeholder.select')}
                    positionFixed={true}
                    multiple={item.multiple || false}
                    v-on:on-change={(val) => { this.handleSelectChange(item.key, val); }}
                >
                    {
                        item.options?.map(opt => (
                            <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>
                        ))
                    }
                </h-select>
            );
        };
        // 生成可搜索的下拉框
        const generateSelectSearch = (item) => {
            return <h-select
                v-model={this.forms[item.key]}
                filterable
                placeholder={item.placeholder || this.$t('common.placeholder.select')}
                positionFixed={true}
                loading={item.loading}
                remote
                remote-method={item.remoteMethod}
                remoteIcon="search"
                loading-text={this.$t('pages.common.loadingLabel')}>
                {
                    item.options.map(opt => { return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>; })
                }
            </h-select>;
        };
        // 生成日期筛选框
        const generateDate = (item) => {
            return <h-datePicker
                type="date"
                placeholder={this.$t('pages.common.selectDateLabel')}
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                placement={item?.placement || 'bottom-start'}
            ></h-datePicker>;
        };
        // 生成日期范围选择框
        const generateDaterange = (item) => {
            return <h-date-picker
                type="daterange"
                confirm
                placement={item?.placement || 'bottom-start'}
                editable={item?.editable || false}
                placeholder={this.$t('pages.common.selectDateLabel')}
                positionFixed={true}
                v-model={this.forms[item.key]}></h-date-picker>;
        };
        // 生成时间选择
        const generateDateTime = (item) => {
            return <h-date-picker
                type="datetime"
                placeholder={this.$t('pages.common.selectDateLabel')}
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                placement={item?.placement || 'bottom-start'}
            ></h-date-picker>;
        };
        // 生成时间范围选择框
        const generateTimerange = (item) => {
            return <h-time-picker
                confirm
                placement={item?.placement || 'bottom-start'}
                type="timerange"
                placeholder={this.$t('pages.common.selectTimeLabel')}
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
            ></h-time-picker>;
        };
        // 生成日期事件范围
        const generateDateTimerange = (item) => {
            return <h-date-picker
                confirm
                placement={item?.placement || 'bottom-start'}
                type="datetimerange"
                placeholder={this.$t('pages.common.selectDateTime')}
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
            ></h-date-picker>;
        };
        // 时延筛选
        const generateTimescreen = (item) => {
            return <div style="display: flex;">
                <h-select
                    v-model={this.forms[item.key]}
                    placeholder={item.placeholder || this.$t('common.placeholder.select')}
                    positionFixed={true}
                    v-on:on-change={this.queryProposal}
                    style="padding-right: 4px;">
                    {
                        item.options.map(opt => {
                            return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>;
                        })
                    }
                </h-select>
                <h-select
                    v-model={this.queryFieldCondition}
                    positionFixed
                    clearable={false}
                    style="width: 50px; padding-right: 4px;">
                    <h-option value="gt">&gt;=</h-option>
                    <h-option value="lt">&lt;=</h-option>
                </h-select>
                <h-poptip
                    // trigger="focus"
                    placement={item.placement || 'bottom'}
                    transfer
                    title={this.proposalTitle}
                    positionFixed
                    width={545}>
                    <h-input
                        v-model={this.beforeFiledValue}
                        disabled={!this.forms[item.key]}
                        placeholder={this.$t('common.placeholder.input')}
                        v-on:on-blur={this.fieldValueBlur}
                        style="width: 60px; top: 0px; padding-right: 4px;"></h-input>
                    <div slot="content" class="api">
                        <h-table border columns={this.columns} data={this.proposalTableList} size='small'></h-table>
                    </div>
                </h-poptip>

                <h-select
                    v-model={this.unit}
                    clearable={false}
                    positionFixed={true}
                    style="width: 50px;"
                >
                    <h-option value="0">s</h-option>
                    <h-option value="1">ms</h-option>
                    <h-option value="2">μs</h-option>
                    <h-option value="3">ns</h-option>
                </h-select>
            </div>;
        };

        const inputGenerators = {
            input: generateInput,
            select: generateSelect,
            selectSearch: generateSelectSearch,
            date: generateDate,
            daterange: generateDaterange,
            dateTime: generateDateTime,
            timerange: generateTimerange,
            datetimerange: generateDateTimerange,
            timescreen: generateTimescreen
        };

        return <h-form
            ref="formValidate"
            props={{ model: this.forms }}
            label-width={85}
            cols="3"
            class="best-form"
        >
            {
                this.formItems.map(item => {
                    return <h-form-item
                        labelTitle={item.label}
                        label={item.label}
                        prop={item.key || ''}
                        required={item.required || false}
                        labelWidth={item?.labelWidth || undefined}
                        validRules={item?.rules}
                    >
                        {(() => {
                            const generator = inputGenerators[item.type];
                            if (generator) {
                                return generator(item);
                            } else {
                                return '';
                            }
                        })}
                    </h-form-item>;
                })
            }
            <style jsx>
                {
                    `
                        .h-poptip-popper[x-placement^="bottom"] {
                            .h-poptip-arrow {
                                border-bottom-color: #fff !important;

                                &::after {
                                    border-bottom-color: #fff !important;
                                }
                            }
                        }
                    `
                }
            </style>
            <input type="text" hidden />
        </h-form>;

    }
};
