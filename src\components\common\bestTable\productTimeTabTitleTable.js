import _ from 'lodash';
import aForm from '@/components/common/form/aForm';
import aTable from '@/components/common/table/aTable';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aTag from '@/components/common/tag/aTag';
import noData from '@/components/common/noData/noData';
import lineChart from '@/components/common/lineChart/lineChart';
import tagList from '@/components/common/tag/tagList';
// import * as echarts from 'echarts';
import './bestTable.less';
export default {
    name: 'productTimeTabTitleTable',
    props: {
        // 默认选中tab
        tabTableKey: {
            type: String,
            default: ''
        },
        tabTableList: {
            type: Array,
            default: () => []
        },
        hasSetTableColumns: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tabName: '',
            formStatus: true,
            visible: false,
            styles: {
                height: 'calc(100% - 55px)',
                paddingBottom: '53px'
            },
            tableHeight: 0
        };
    },
    watch: {
        tabName(newVal) {
            this.$emit('changeTab', newVal);
            this.$nextTick(() => {
                this.fetTableHeight();
            });
        }
    },
    mounted() {
        this.$_init();
        window.addEventListener('resize', this.fetTableHeight);
        this.$nextTick(() => {
            this.fetTableHeight();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                if (window.innerHeight <= 500){
                    this.formStatus = false;
                }
                const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
                const height = this.formStatus ? Math.ceil(this.tabTableList[index].formItems.length / 3) * 45 : 0;
                this.tableHeight = this.$refs[`table-box${index}`] && this.$refs[`table-box${index}`].getBoundingClientRect().height - height - 200;
            });
        },
        changeFormStatus(){
            this.formStatus = !this.formStatus;
            if (window.innerHeight <= 500){
                this.formStatus = false;
                this.$hMessage.info(this.$t('pages.common.windowHeightWarning'));
            }
            this.fetTableHeight();
        },
        $_init() {
            this.tabName = this.tabTableKey;
        },
        setTableColumns() {
            this.visible = true;
        },
        // 获取查询参数
        getQueryParam() {
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            const param = this.$refs[`forms${index}`]?.query();
            if (!param) return;
            const pageParam = this.tabTableList[index].tabTableData[0].hasPage ? this.$refs[`table${index}`]?.getPageData() : {};
            return { ...param, ...pageParam };
        },
        $_handleQuery(val) {

            if (val && Object.keys(val).length){
                this.$emit('query', { ...val });
            } else {
                const tab = _.find(this.tabTableList, ['key', this.tabName]);
                tab.queryId = '';
                const params = this.getQueryParam();
                if (!params) return;
                this.$emit('query', params);
            }
        },
        $_handleReset() {
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            this.tabTableList[index].queryId = '';
            this.$refs[`forms${index}`].reset();
        },
        // 点击查询
        handleClickQuery() {
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            const param = this.$refs[`forms${index}`].query();
            if (!param) return;
            this.$refs[`table${index}`].resetPage();
            this.$_handleQuery();
        },
        // 切换产品-初始化查询
        $_handleResetPageData() {
            this.tabTableList.forEach((ele, index) => {
                this.$refs[`table${index}`].resetPage();
                this.$refs[`table${index}`].resetPageSize();
                this.$refs[`table${index}`].resetSortData();
                this.$refs[`tag-list${index}`] && this.$refs[`tag-list${index}`].scrollReset();
            });
        },
        // 保存
        handleClickSave(){
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            const param = this.$refs[`forms${index}`]?.query();
            if (!param) return;
            this.$emit('save', { ...param });
        },
        // 删除tag
        handleTagClose(id) {
            this.$emit('tagClose', id);
        },
        // 切换tag重置查询数据
        handleTagClick(id){
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            this.$refs[`table${index}`].resetPage();
            this.$refs[`table${index}`].resetPageSize();
            this.$refs[`table${index}`].resetSortData();
            this.tabTableList[index].queryId = id || '';
            let param = {};
            this.$emit('tagClick', this.tabTableList[index].queryId, val => {
                param = val;
            });
            this.$refs[`forms${index}`].echoFormData({ ...param });
            this.$_handleQuery({ ...param });
        },
        // 当前tag下重置分页数据
        handleTagQuery(){
            let param = {};
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            if (this.tabTableList[index].queryId){
                this.$emit('tagClick', this.tabTableList[index].queryId, val => {
                    param = val;
                });
                const pageParam = this.tabTableList[index].tabTableData[0].hasPage ? this.$refs[`table${index}`]?.getPageData() : {};
                this.$_handleQuery({ ...param, ...pageParam });
            } else {
                this.$_handleQuery();
            }
        },
        // 重置事件滚动
        scrollReset(){
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            this.$refs[`tag-list${index}`] && this.$refs[`tag-list${index}`].scrollReset();
        },
        // 监听下拉框变化
        handleSelectChange(key, val) {
            this.$emit('handleSelectChange', key, val);
        },
        // 获取时延指标查询建议
        queryProposal(span) {
            this.$emit('queryProposal', span);
        }
    },
    components: { aForm, aTable, aButton, aTitle, aTag, noData, tagList, lineChart },
    render() {
        return <div class="best-table">
            <div class="btn-list">
                <a-button type="primary" onClick={this.handleClickQuery}>{this.$t('common.query')}</a-button>
                <a-button type="dark" onClick={this.$_handleReset}>{this.$t('common.reset')}</a-button>
                {this.hasSetTableColumns && <a-button type="dark" onClick={this.setTableColumns}>{this.$t('common.configTable')}</a-button>}
                <a-button type="dark" onClick={this.changeFormStatus}>
                    {this.formStatus ? this.$t('common.collapse') : this.$t('pages.common.expandQuery')}
                    <h-icon name={this.formStatus ? 'packup' : 'unfold'}></h-icon></a-button>
                <a-button type="dark" onClick={this.handleClickSave}>{this.$t('common.save')}</a-button>
            </div>
            <h-tabs v-model={this.tabName}>
                {
                    this.tabTableList.map((item, idx) => {
                        return <h-tab-pane label={item.label} name={item.key}>
                            <div class="form-box" style={{ height: this.formStatus ? Math.ceil(item.formItems.length / 3) * 45 + 'px' : 0 }}>
                                <a-form
                                    ref={`forms${idx}`}
                                    formItems={item.formItems}
                                    proposalList={item.proposalList}
                                    v-on:queryProposal={this.queryProposal}
                                    v-on:handleSelectChange={this.handleSelectChange} />
                            </div>
                            <div>
                                <a-title title={this.$t('pages.common.quickQuery')}>
                                    <slot>
                                        <tag-list
                                            ref={`tag-list${idx}`}
                                            queryList={item.queryList}
                                            selectedId={item.queryId}
                                            v-on:on-close={this.handleTagClose}
                                            v-on:on-click={this.handleTagClick}
                                            style="width: calc(100% - 100px); left: 100px;"
                                        >
                                        </tag-list>
                                    </slot>
                                </a-title>
                            </div>
                            <h-tabs v-model={item.tabName}  animated={false}>
                                {
                                    item.tabTableData.map((ele, index) => {
                                        switch (ele.type) {
                                            case 'table':
                                                return  <h-tab-pane key={ele.name} label={ele.label} name={ele.name}>
                                                    <div ref={`table-box${idx}`} class='table-box'>
                                                        <a-table
                                                            ref={`table${idx}`}
                                                            tableData={ele.tableData}
                                                            columns={ele.columns}
                                                            hasPage={ele.hasPage}
                                                            total={ele.total}
                                                            loading={item.tableLoading}
                                                            v-on:query={this.handleTagQuery}
                                                            height={this.tableHeight}
                                                        />
                                                    </div>
                                                </h-tab-pane>;
                                            default:
                                                return '';
                                        }
                                    })
                                }
                            </h-tabs>
                        </h-tab-pane>;
                    })
                }
            </h-tabs>

            <style jsx>
                {
                    `
                        .form-box {
                            margin-top: 10px;
                            overflow-y: hidden;
                            transition: height 1s;
                        }
                        .bg-none {
                            width: 100%;
                            height: calc(100% - 200px);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                    `
                }
            </style>
        </div>;
    }
};
