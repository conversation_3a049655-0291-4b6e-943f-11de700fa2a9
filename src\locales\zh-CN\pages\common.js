export default {
    imageNotDisplayed: '图片无法显示',
    noData: '无数据',
    invalidQueryMethod: '查询方法不正确',
    unsupportedComponentError: '不支持的组件类型: {type}， 请补充该组件',
    noMatchingDataDescription: '没有找到匹配的数据，可尝试其它关键词进行搜索',
    invalidRemoteQueryMethod: '远程查询方法不正确',
    selectLabel: '选择',
    hideSearch: '隐藏搜索',
    expandSearch: '展开搜索',
    todayIsLabel: '今天是',
    layoutCacheWarning: 'id、itemKey、layout 为空，不存储瀑布流布局数据',

    // dataExportModal and exportHistoryModal
    exportDataLabel: '数据导出',
    queryFieldName: '查询字段名',
    inputAliasPrompt: '请输入别名',
    exportFileLabel: '导出文件',
    exportProgress: '导出进度',
    exporting: '导出中',
    startExport: '开始导出',
    terminatingLabel: '终止中',
    terminateExport: '终止导出',
    exportFailedMessage: '导出失败！',
    operationSuccess: '操作成功',
    operationFailed: '操作失败',
    exportFieldConfig: '导出字段配置',
    exportFieldName: '导出字段名',
    exportExecutionStatus: '导出执行状态',
    downloadLabel: '下载',
    executionInfo: '执行信息',
    exportTimeLabel: '导出时间',
    exportFileDetail: '导出文件',
    fileSize: '文件大小',
    fileStorageDirectory: '文件存储目录',
    spaceOccupied: '占用空间',
    testCaseName: '用例名称',
    caseCreationSuccess: '用例创建成功!',
    caseCreationFailed: '用例创建失败!',

    // For bestTable, aform, aFormDashboard
    clearDataButton: '清空数据',
    quickQuery: '快捷查询',
    windowHeightWarning: '当前窗口高度小于500,请放大窗口查看!',
    expandQuery: '展开查询',
    exportDataAction: '导出数据',
    exportHistory: '导出历史',
    loadingLabel: '加载中',
    selectDateLabel: '选择日期',
    selectDateTime: '选择日期时间',
    periodLabel: '周期',
    averageLabel: '平均数',
    productDelayYesterday: '产品昨日时延',
    productDelayLastWeek: '产品上周时延',
    productDelayLastMonth: '产品上月时延',
    latencyDataSuggestion: '时延指标数据建议(单位',
    selectTimeLabel: '选择时间',
    queryResults: '查询结果',
    addTagButton: '添加标签',
    deleteTagButton: '删除标签',
    batchDelete: '批量删除',

    // aFormDashboard
    // aForm
    period: '周期',
    minValue: '最小值',
    averageValue: '平均数',
    medianValue: '中位数',
    maxValue: '最大值',
    paramNotEmptyLabel: '参数不能为空',
    enterPositiveIntegerPrompt: '请输入正整数',
    integerOverflowDescription: '输入超出最大安全整数值',
    minValueGreaterMessage: '最小值不能大于最大值',
    rangeDifferenceNotAllowedMessage: '范围差值不能>=1000',
    maxValueSmallerMessage: '最大值不能小于最小值',

    // ainput
    notAllLetters: '不全是字母',
    inputNotEmptyLabel: '输入不能为空',
    enterKeyword: '请输入关键字',
    notIntegerFormat: '输入的不是整数格式',
    notPositiveIntegerFormat: '输入的不是正整数格式',
    notNegativeIntegerFormat: '输入的不是负整数格式',
    onlyNumberFormat: '只能输入数字格式',
    onlyNonNegativeIntegerFormat: '只能输入非负整数数字格式',
    onlyNonPositiveIntegerFormat: '只能输入非正整数数字格式',
    onlyFloatingNumberFormat: '只能输入浮点数格式',
    onlyPositiveFloatingNumberFormat: '只能输入正浮点数格式',
    onlyNegativeFloatingNumberFormat: '只能输入负浮点数格式',
    onlyNonNegativeFloatingNumberFormat: '只能输入非负浮点数格式',
    onlyNonPositiveFloatingNumberFormat: '只能输入非正浮点数格式',
    notEmailFormat: '邮件地址不正确',
    onlyColorFormat: '只能输入颜色格式',
    onlyUrlFormat: '只能输入url格式',
    onlyChineseFormat: '只能输入中文格式',
    onlyACSIIFormat: '只能输入ACSII字符格式',
    invalidPostalCode: '邮编输入格式不正确，请输入6位编码',
    invalidMobileFormat: '移动电话格式不正确',
    onlyIPv4Format: '只能输入ip4地址格式',
    onlyImageFormat: '只能输入图片格式',
    onlyCompressedFileFormat: '只能输入压缩文件格式',
    invalidDateFormat: '日期格式不正确',
    invalidQQFormat: 'QQ号码格式不正确',
    invalidPhoneFormat: '电话号码格式不正确',
    onlyAlphanumericUnderscore: '只能输入由数字、26个英文字母或者下划线组成的字符串',
    onlyLetterFormat: '只能输入字母格式',
    onlyUppercaseFormat: '只能输入大写字母格式',
    onlyLowercaseFormat: '只能输入小写字母格式',

    // ObserveHead
    noDataAvailable: '暂无数据',
    auctionCollection: '集合竞价',
    morningTradeAuction: '早盘竞价交易',
    marketBreak: '盘休',
    afternoonTradeAuction: '午盘竞价交易',
    closeOfDayPricing: '盘后定价',

    // topo  ldpNodeTopo
    coreDataStaging: '核心数据上场',
    coreDataSynchronization: '核心数据同步',
    connectionRelation: '连接关系',
    inMemoryDataManagement: '内存数据管理',
    managementFunction: '管理功能',
    instanceObservation: '实例观测',
    contextIDLabel: '上下文ID',
    contextNameLabel: '上下文名称',
    configurationDetail: '配置详情',
    performanceMetrics: '运行指标',
    serviceNameLabel: '服务名',
    nextClusterCount: '下一层集群个数',
    shardCount: '分片数',
    clusterMemberCount: '集群成员个数',
    aliveMemberCount: '存活成员个数',
    clusterStatusLabel: '集群状态',
    lastReplyTime: '最后响应时间',
    applicationNodeVersion: '应用节点版本',
    applicationDevelopmentPlatform: '应用开发平台',
    serverDeployment: '部署服务器',
    managementPortLabel: '管理端口',
    nodeStatusLabel: '节点状态',
    lastHeartbeatTime: '最后心跳时间',
    primaryLabel: '主',
    backupLabel: '备',
    messageCopy: '消息复制',
    stateMachineCopy: '状态机复制',
    dataStagingLabel: '数据上场中',
    dataStagingComplete: '数据上场完成',
    systemReady: '系统就绪',
    dataOfflining: '数据下场中',
    dataOfflineComplete: '数据下场完成',
    systemOffline: '系统已下线',
    dataStagingFailed: '数据上场失败',
    dataOffliningFailed: '数据下场失败',
    rollbackComplete: '已回切',
    basicInfoLabel: '基础信息',
    serviceNameDetail: '服务名',
    nextClusterCountDetail: '下一层集群个数',
    shortcutsLabel: '快捷导航',
    coreDataStagingDetail: '核心数据上场',
    coreDataSyncDetail: '核心数据同步',
    clusterMembersLabel: '集群成员',
    clusterRoleLabel: '集群角色',
    nodeInformation: '节点信息',
    clusterNameDetail: '集群名称',
    clusterMode: '集群模式:',
    runningStatus: '运行状态',
    clusterMemberCountDetail: '集群成员个数',
    aliveMemberCountDetail: '存活成员个数',
    clusterStatusDetail: '集群状态',
    lastResponseTimeDetail: '最后响应时间',
    connectionRelationDetail: '连接关系',
    memoryDataManagementDetail: '内存数据管理',
    managementFunctionDetail: '管理功能',
    instanceObservationDetail: '实例观测',
    applicationNodeName: '应用节点名',
    applicationNodeType: '应用节点类型',
    applicationNodeVersionDetail: '应用节点版本',
    applicationPlatformDetail: '应用开发平台',
    serverDeploymentDetail: '部署服务器',
    clusterRoleDetail: '集群角色',
    managementPortDetail: '管理端口',
    nodeStatusDetail: '节点状态',
    businessStatus: '业务状态',
    lastHeartbeatTimeDetail: '最后心跳时间',
    contextIDDetail: '上下文ID',
    contextNameDetail: '上下文名称',
    roleDetail: '角色:',
    contextVersionDetail: '上下文版本',
    syncModeLabel: '同步模式',
    onlineStatus: '是否在线',
    configurationDetailLabel: '配置详情',
    performanceMetricsLabel: '运行指标'
};
