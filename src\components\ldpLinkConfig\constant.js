// ---------------------------------------------------    服务     -----------------------------------------

/**
* 平台功能号列配置
*/
export const initializeLdpFuncColumns = [
    { title: '管理功能号', key: 'funcName', ellipsis: true, minWidth: 150 },
    { title: '插件', key: 'pluginName', ellipsis: true, minWidth: 120 },
    { title: '功能号名称', key: 'funcNameCn', ellipsis: true,
        minWidth: 300,
        render: (h, params) => {
            const funcNameCn = params.row?.funcNameCn || '-';
            return h('div', {
                attrs: {
                    title: funcNameCn
                },
                class: 'h-table-cell-ellipsis'
            }, funcNameCn);
        } },
    { title: '功能说明', key: 'description', ellipsis: true,
        minWidth: 300,
        render: (h, params) => {
            const description = params.row?.description || '-';
            return h('div', {
                attrs: {
                    title: description
                },
                class: 'h-table-cell-ellipsis'
            }, description);
        }
    }
];

/**
* 业务功能号列配置
*/
export const initializeBusFuncColumns = [
    { title: '功能号', key: 'funcNo', ellipsis: true },
    { title: '功能号名', key: 'funcNoName', ellipsis: true },
    { title: '子系统号', key: 'systemNo', ellipsis: true },
    { title: '读写标识', key: 'flag', ellipsis: true },
    { title: '可运行的业务状态', key: 'allowBizStatus', ellipsis: true },
    { title: '定位模式', key: 'locateMode', ellipsis: true }
];

/**
 * 数据分片列配置
 */
export const initializeConfigColumns = [
    {
        title: '分片号',
        key: 'shardingNo',
        ellipsis: true,
        formatMethod: (row) => row?.shardingNo ?? '-'
    },
    {
        title: '数据源',
        key: 'dataSource',
        ellipsis: true,
        formatMethod: (row) => row?.dataSource || '-'
    },
    {
        title: '分片key',
        key: 'shardingKey',
        renderHeader: (h) =>
            h('div', [
                h(
                    'span',
                    {
                        style: {
                            color: '#fff',
                            verticalAlign: '2px',
                            'margin-right': '5px'
                        }
                    },
                    '分片key'
                ),
                h(
                    'poptip',
                    {
                        class: 'apm-poptip',
                        props: { trigger: 'hover', placement: 'right' }
                    },
                    [
                        h('icon', {
                            props: { name: 'prompt1', color: '#9296A1', size: '14' }
                        }),
                        h(
                            'div',
                            {
                                slot: 'content',
                                class: 'pop-content',
                                style: { padding: '5px 10px' }
                            },
                            [h('p', '定位串')]
                        )
                    ]
                )
            ]),
        ellipsis: true,
        formatMethod: (row) => row?.shardingKey ?? '-'
    },
    {
        title: '数据区间',
        key: 'dataRanges',
        ellipsis: true,
        render: (h, params) => {
            const content = [];
            content.push(h('span', params?.row?.['dataRanges']?.join(',') || '-'));
            return h('span', content);
        }
    },
    {
        title: '关联集群',
        key: 'clusterNames',
        ellipsis: true,
        render: (h, params) =>
            h('span', params?.row?.clusterNames?.join(',') || '-')
    }
];

/**
* 应用注册列配置
*/
export const initializeApplicationColumns = [
    { title: '服务提供者', key: 'provider', ellipsis: true },
    { title: '服务集群地址', key: 'address', ellipsis: true, minWidth: 300 },
    { title: '应用节点注册中心路径', key: 'path', ellipsis: true }
];

/**
* 仲裁中心列配置
*/
export const initializeArbitrationColumns = [
    { title: '服务提供者', key: 'provider', ellipsis: true },
    { title: '服务集群地址', key: 'address', ellipsis: true, minWidth: 300 },
    { title: '集中仲裁服务路径', key: 'path', ellipsis: true }
];

/**
* 配置中心列配置
*/
export const initializeConfigurationColumns = [
    { title: '配置提供者', key: 'sourceType', ellipsis: true },
    { title: '配置服务地址', key: 'serviceAddress', ellipsis: true, minWidth: 300 },
    { title: '配置根目录', key: 'path', ellipsis: true, minWidth: 300 },
    { title: '配置节点名称', key: 'name', ellipsis: true }
];

// ----------------------------------------------------------   总览     --------------------------------------------------------

/**
 * 生成表格组信息。
 *
 * @param {*} apmDirDesc - apm应用字典数据。
 * @returns {Array} - 返回一个多维数组，每个元素代表一个特定类型（例如应用、集群等）的表格配置，包括表头信息和初始化的空数据。
 *
 */
export const generateTableGroupInfo = (apmDirDesc) => [
    {
        title: { label: '应用' },
        columns: [
            {
                title: '应用类型',
                key: 'instanceType',
                render: (h, params) => {
                    return h(
                        'div',
                        apmDirDesc?.appTypeDictDesc?.[params.row.instanceType] || params.row.instanceType
                    );
                }
            },
            {
                title: '关联应用',
                key: 'instanceNumber',
                minWidth: 90
            }
        ],
        tableData: []
    },
    {
        title: { label: '集群' },
        columns: [
            {
                title: '集群名',
                key: 'clusterName'
            },
            {
                title: '集群类型',
                key: 'clusterType',
                render: (h, params) => {
                    return h(
                        'div',
                        apmDirDesc?.clusterTypeDict?.[params.row.clusterType] || params.row.clusterType
                    );
                }
            },
            {
                title: '关联应用',
                key: 'instanceNumber',
                minWidth: 90
            }
        ],
        tableData: []
    },
    {
        title: { label: '服务' },
        columns: [
            {
                title: '服务类型',
                key: 'serviceName'
            },
            {
                title: '关联应用',
                key: 'instanceNumber',
                minWidth: 90
            }
        ],
        tableData: []
    },
    {
        title: { label: '服务器' },
        columns: [
            {
                title: '主机名',
                key: 'hostName',
                minWidth: 110
            },
            {
                title: '主机别名',
                key: 'hostNameAlias',
                minWidth: 110,
                formatMethod: (row) => row?.hostNameAlias || '-'
            },
            {
                title: 'IP地址/域名',
                key: 'ips',
                minWidth: 110,
                render: (h, { row }) => {
                    const ips = row.ips ? row.ips?.join(',') : '-';
                    return h('div', ips);
                }
            },
            {
                title: '关联应用',
                key: 'instanceNumber'
            }
        ],
        tableData: []
    },
    {
        title: { label: '机房' },
        columns: [
            {
                title: '机房名',
                key: 'roomName'
            },
            {
                title: '机房别名',
                key: 'roomNameAlias',
                formatMethod: (row) => row?.roomNameAlias || '-'
            },
            {
                title: '关联应用',
                key: 'instanceNumber',
                minWidth: 90
            }
        ],
        tableData: []
    },
    {
        title: { label: '应用分片' },
        columns: [
            {
                title: '分片号',
                key: 'shardingNo'
            },
            {
                title: '分片名',
                key: 'shardingName'
            },
            {
                title: '关联应用',
                key: 'instanceNumber',
                minWidth: 90
            }
        ],
        tableData: []
    }
];
