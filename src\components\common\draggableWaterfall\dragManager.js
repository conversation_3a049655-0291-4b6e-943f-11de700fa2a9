/**
 * 拖拽数据管理
 */
export class DragManager {

    constructor() {
        this.revert();
        this.EDGE_THRESHOLD = 100;    // 靠近阈值
    }

    revert() {
        this.dragging = false;   // 是否在拖拽中
        this.containeLeft = undefined;    // 容器左边缘
        this.containeWith = undefined;    // 容器宽度
    }

    /**
     * 开始拖拽
     */
    startDrag(containeLeft, containeWith) {
        this.dragging = true;
        this.containeLeft = containeLeft;
        this.containeWith = containeWith;
    }

    /**
     * 结束拖拽
     */
    endDrag() {
        this.revert();
    }

    /**
     * 判断是否靠近容器左边缘
     */
    nearContainerLeft(event) {
        if (!this.dragging || this.containeLeft === undefined) {
            return false;
        }

        const offsetX = event.clientX - this.containeLeft;
        return offsetX < this.EDGE_THRESHOLD;
    }

    /**
     * 判断是否靠近容器右边缘
     */
    nearContainerRight(event) {
        if (!this.dragging || this.containeLeft === undefined || this.containeWith === undefined) {
            return false;
        }

        const offsetX = event.clientX - this.containeLeft;
        return this.containeWith - offsetX < this.EDGE_THRESHOLD;
    }
}
