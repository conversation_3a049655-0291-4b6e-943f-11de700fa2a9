export default {
    title: 'Application Check Configuration',
    filterCondition: 'Filter Condition',
    threadInfo: 'Thread Information',
    manageIps: 'Management IP',
    selectManagementIP: 'Please select management IP',
    appInstanceNames: 'Application Node Name',
    selectApplicationNode: 'Please select application node name',
    threadOwner: 'Thread Owner',
    threadOwnerInputMessage: 'Please input thread owner',
    cpuAffinityLabel: 'CPU Affinity',
    selectCpuAffinity: 'Please select CPU affinity',
    cpuAffinityTrue: 'Yes',
    cpuAffinityFalse: 'No',
    threadName: 'Thread Name',
    threadPriority: 'Thread Type',
    threadCpuNo: 'CPU Binding'
};
