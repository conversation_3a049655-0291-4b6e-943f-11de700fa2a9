import i18n from '@/locales/i18n';

// analyseData
export const loopList = [
    {
        value: 'Rsp',
        label: i18n.t('config.coreRsp')
    },
    {
        value: 'RtnCfmMbr',
        label: i18n.t('config.coreConfirm')
    },
    {
        value: 'RtnCfmXchg',
        label: i18n.t('config.tradeConfirm')
    },
    {
        value: 'RtnTrd',
        label: i18n.t('config.tradeDeal')
    }
];

export const comparatorConfig = {
    default: [
        {
            value: '=',
            label: '='
        },
        {
            value: '>=',
            label: '>='
        },
        {
            value: '>',
            label: '>'
        },
        {
            value: '<=',
            label: '<='
        },
        {
            value: '<',
            label: '<'
        }
    ],
    bool: [
        {
            value: '=',
            label: '='
        }
    ],
    char: [
        {
            value: 'like',
            label: '='
        },
        {
            value: 'contain',
            label: '⊇'
        }
    ]
};

// 应用监控实例信息字典-businessPoptip.js
export const instanceInfoDict = {
    // 分组名称字典
    groupName: {
        baseInfo: i18n.t('config.baseInfo'),
        runningInfo: i18n.t('config.runningInfo'),
        extInfo: i18n.t('config.extInfo'),
        operateInfo: i18n.t('config.operateInfo')
    },
    // 基础信息
    baseInfo: {
        instanceName: i18n.t('config.instanceName'),
        instanceDesc: i18n.t('config.instanceDesc'),
        version: i18n.t('config.version'),
        developPlatform: i18n.t('config.developPlatform')
    },
    // 运行信息
    runningInfo: {
        ip: i18n.t('config.ip'),
        port: i18n.t('config.port'),
        clusterRole: i18n.t('config.clusterRole')
    },
    // 扩展信息
    extInfo: {
        collectionType: i18n.t('config.collectionType'),
        kafkaAddress: i18n.t('config.kafkaAddress'),
        prometheusAddress: i18n.t('config.prometheusAddress'),
        kafkaTopicList: i18n.t('config.kafkaTopicList'),
        indexName: i18n.t('config.indexName'),
        dataReceivePort: i18n.t('config.dataReceivePort'),
        targetInstanceName: i18n.t('config.targetInstanceName'),
        dataDir: i18n.t('config.dataDir'),
        processName: i18n.t('config.processName')
    }
};

