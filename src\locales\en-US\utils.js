export default {
    // IndexedDB
    clearDatabaseSuccessMessage: 'Database cleared successfully',
    clearDatabaseErrorMessage: 'Error occurred while clearing database:',
    // pkcs8Rsa
    encryptionFailureMessage: 'RSA encryption failed:',

    // roomClusterNode roomSeviceClusterNode
    totalItems: 'All',
    unknownCluster: 'Unknown Cluster',
    unknownService: 'Unknown Service',

    // utils
    invalidTimeFormatError: 'Invalid time format',
    stringToObjectConversionError: 'String to object conversion failed:',
    jsonParsingError: 'JSON parsing failed:',
    abnormalTimeIntervalError: 'Time interval abnormal: {previousTime} → {currentTime}, using default 1 second',
    trillions: 'Trillion',
    billions: 'Billion',
    tenThousands: 'Ten Thousand',

    // validate
    emptyInputError: 'Input cannot be empty',
    numberInputError: 'Please enter a number type',
    integerInputLabel: 'Please enter an integer number',
    unsignedIntegerRangeError: 'Input range exceeded, input range should be -2147483648 ~ 2147483647',
    unsignedIntegerRangeError1: 'Input range exceeded, input range should be 0 ~ 4294967295',
    unsignedIntegerRangeError2: 'Input range exceeded, input range should be -32768 ~ 32767',
    unsignedIntegerRangeError3: 'Input range exceeded, input range should be 0 ~ 65535',
    unsignedIntegerRangeError4: 'Input range exceeded, input range should be -9223372036854775808 ~ 9223372036854775807',
    unsignedIntegerRangeError5: 'Input range exceeded, input range should be 0 ~ 18446744073709551615',
    unsignedIntegerRangeError6: 'Byte count exceeds input range',
    floatRangeError: 'Input range exceeded, input range should be -3.40282e38 ~ 3.40282e38',
    doubleRangeError: 'Input range exceeded, input range should be -1.79769e308 ~ 1.79769e308',
    decimalRangeError: 'Input range exceeded, input range should be -1.19e4932 ~ 1.19e4932',
    booleanInputLabel: 'Please enter 0 or 1',
    portNumberInputLabel: 'Please enter a port number between 1 and 65535',
    passwordLabel: 'Please enter password',
    charLengthExceedError: 'Character length cannot exceed {length}!',
    passwordComplexityDescription: 'Must contain at least 3 types of characters: letters (a~zA~Z), numbers (0~9), special symbols (!, $, #, @, *, _), length 8~20 characters',
    numberInputLabel: 'Please enter a number',
    addressCountDescription: 'Use IP:PORT format, multiple addresses separated by English commas',
    booleanTypeError: 'Boolean type only supports true, false',
    validArrayFormatLabel: 'Please enter a valid array format',
    validObjectFormatLabel: 'Please enter a valid object format',
    rangeExceedErrorMsg: '{item} duplicate',
    rangeExceedErrorMsg1: 'Input range exceeded:',
    rangeExceedErrorMsg2: 'Field length exceeded:',
    rangeExceedErrorMsg3: 'Array length cannot exceed {maxLength} elements',
    rangeExceedErrorMsg4: 'Array length cannot be less than {minLength} elements',
    rangeExceedErrorMsg5: 'Object property count cannot exceed {maxProperties}',
    rangeExceedErrorMsg6: 'Object property count cannot be less than {minProperties}',
    rangeExceedErrorMsg7: 'Missing required properties:',
    rangeExceedErrorMsg8: 'Input range exceeded, input range should be {minValue} ~ {maxValue}',

    // websocket
    closeConnect: 'WebSocket connection closed',
    connectSuccess: 'Connection successful',
    abnormal: 'WebSocket exception',
    connectAbnormal: 'WebSocket connection exception!'
};
