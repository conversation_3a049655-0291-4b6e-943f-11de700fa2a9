import i18n from '@/locales/i18n';

/*
 * @Description: 浏览器数据库
 * @Author: <PERSON><PERSON>
 * @Date: 2023-08-29 10:16:40
 * @LastEditTime: 2023-08-31 19:08:34
 * @LastEditors: Z<PERSON> Ying
 */
export class IndexedDB {
    // eslint-disable-next-line max-params
    constructor(dbName, storeNames, version, callback) {
        this.storeNames = Array.isArray(storeNames) ? storeNames : [storeNames];
        const indexedDB = window.indexedDB || window.webkitIndexedDB || window.mozIndexedDB;
        const request = indexedDB.open(dbName, version);

        request.onsuccess = e => {
            this.db = e.target.result;
            callback(this);
            console.log('Init indexedDB successfully');
        };
        // 在数据库创建或者版本更新时，会触发onupgradeneeded事件 在不指定的情况下，默认版本号为1
        // https://cloud.tencent.com/developer/article/1341973
        request.onupgradeneeded = e => {
            this.db = e.target.result;
            this.storeNames.forEach(name => {
                if (!this.db.objectStoreNames.contains(name)) {
                    this.db.createObjectStore(name);
                }
            });
            console.log('DB version changed, db version: ', this.db.version);
        };
        request.onerror = e => { console.info('Can not open indexedDB', e); };
    }

    get(key, storeName) {
        return new Promise((resolve) => {
            const name = storeName || this.storeNames[0];
            const transaction = this.db.transaction([name]);
            const objectStore = transaction.objectStore(name);
            const request = objectStore.get(key);
            request.onsuccess = e => { resolve(e.target.result); };
            request.onerror = e => { resolve(''); };
        });
    }

    async set(value, key, storeName) {
        let oldValue = '';
        oldValue = await this.get(key, storeName);
        if (oldValue) {
            this.update(value, key, storeName);
        } else {
            const name = storeName || this.storeNames[0];
            const transaction = this.db.transaction([name], 'readwrite');
            const objectStore = transaction.objectStore(name);
            const request = objectStore.add(value, key);
            request.onerror = e => { console.info('Can not add value', e); };
        }
    }

    readAll(callback, storeName) {
        const name = storeName || this.storeNames[0];
        const transaction = this.db.transaction([name]);
        const objectStore = transaction.objectStore(name);
        const request = objectStore.openCursor();
        const data = [];
        request.onsuccess = e => {
            const cursor = e.target.result;
            if (cursor) {
                const { key, value } = cursor;
                data.push({ key, value });
                cursor.continue();
            } else {
                callback(data);
            }
        };
    }

    async update(newValue, key, storeName) {
        let oldValue = '';
        oldValue = await this.get(key, storeName);
        if (!oldValue) {
            this.set(newValue, key, storeName);
        } else {
            const name = storeName || this.storeNames[0];
            const transaction = this.db.transaction([name], 'readwrite');
            const objectStore = transaction.objectStore(name);
            const request = objectStore.put(newValue, key);
            request.onerror = e => { console.info('Can not update value', e); };
        }
    }

    fuzzySearchData(field, keywords, options = {}) {
        const { callback, storeName } = options;
        const name = storeName || this.storeNames[0];
        const transaction = this.db.transaction([name]);
        const objectStore = transaction.objectStore(name);
        const request = objectStore.openCursor();
        const data = [];
        request.onsuccess = e => {
            const cursor = e.target.result;
            if (cursor) {
                if (cursor.value[`${field}`].indexOf(keywords) >= 0) {
                    data.push(cursor.value);
                }
                cursor.continue();
            } else {
                callback && callback(data);
            }
        };
    }

    remove(key, storeName) {
        const name = storeName || this.storeNames[0];
        const request = this.db.transaction([name], 'readwrite')
            .objectStore(name)
            .delete(key);
        request.onerror = e => { console.info('Can not remove value', e); };
    }

    close() {
        this.db.close();
    }

    clear(storeName) {
        const name = storeName || this.storeNames[0];
        const transaction = this.db.transaction([name], 'readwrite');
        const objectStore = transaction.objectStore(name);
        const clearRequest = objectStore.clear();
        clearRequest.onsuccess = function(event) {
            console.log(i18n.t('utils.clearDatabaseSuccessMessage'));
        };
        clearRequest.onerror = function(event) {
            console.error(i18n.t('utils.clearDatabaseErrorMessage'), event.target.error);
        };
    }
}
