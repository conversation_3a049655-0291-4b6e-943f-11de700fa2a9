export default {

    /**
     * 标识符，存储时的标志位
     */
    id: {
        type: String,
        required: true
    },

    /**
     * 是否启用历史布局
     * 若启用，取 localStorage 中 id 存储的值，同时编辑完后会存储布局值至 localStorage
     */
    history: {
        type: Boolean,
        default: false
    },

    /**
     * 瀑布流元素集合
     */
    items: {
        type: Array,
        default: () => []
    },

    /**
     * 元素唯一标识（用于 key）
     */
    itemKey: {
        type: String,
        default: 'key'
    },

    /**
     * 元素间距，单位 px
     * 支持传入 [0, 0]，分别代表列间距和元素上下间距
     */
    gap: {
        type: [Number, String, Array],
        default: 0
    },

    /**
     * 元素最小宽度
     * cols 不存在的时候，以该值计算列宽
     */
    minWidth: {
        type: Number
    },

    /**
     * 列数
     * 支持响应式布局：{ 600: 1, 1200: 2 }，表示容器宽度 600px 以内的显示 1 列，1200 px 以内显示 2 列
     * 当历史布局生效、拖拽模式下，该值失效
     * 当实际元素不足设置的值时，以实际元素为准
     */
    cols: {
        type: [Number, Object]
    },

    /**
     * 是否启用等高列瀑布流布局；
     * 若不启用，则按顺序插入元素
     * 仅在 draggable 为 false 的情况下生效
     */
    isEqualHeight: {
        type: Boolean,
        default: false
    }
};
