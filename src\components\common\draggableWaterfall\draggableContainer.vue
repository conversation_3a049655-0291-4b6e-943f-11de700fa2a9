<template>
    <div ref="draggableContainer" class="draggable-container" :style="containerStyle">
        <template v-for="item of items">
            <div :key="item[itemKey]" class="drag-item">
                <slot :item="item"></slot>
            </div>
        </template>
    </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import Sortable from 'sortablejs';

/**
 * 基于 sortable.js 可拖拽容器
 */
export default {
    name: 'DraggableContainer',

    model: {
        prop: 'items',
        event: 'update:items'
    },

    props: {
        items: {
            type: Array,
            required: true
        },

        /**
         * 支持 sortable.js 属性
         */
        options: {
            type: Object,
            default: () => ({})
        },

        /**
         * 自定义样式
         */
        containerStyle: {
            type: Object,
            default: () => ({})
        },

        /**
         * 元素标志位
         */
        itemKey: {
            type: String,
            required: true
        }
    },

    watch: {
        'options.disabled'(disabled) {
            this.sortable.option('disabled', disabled);
        }
    },

    mounted() {
        this.initSortable();
    },

    beforeDestroy() {
        if (this.sortable) {
            this.sortable.destroy();
        }
    },
    methods: {
        initSortable() {
            const group =
                typeof this.options.group === 'string'
                    ? { pull: 'clone', name: this.options.group }
                    : { pull: 'clone', ...this.options.group };

            this.sortable = Sortable.create(this.$refs.draggableContainer, {
                animation: 100,
                scroll: true,
                forceFallback: true,
                forceAutoScrollFallback: true,
                scrollSensitivity: 60,
                scrollSpeed: 20,
                ghostClass: 'drag-ghost-item', // 拖拽占位符样式
                chosenClass: 'drag-chosen-item', // 选中元素时的样式
                fallbackClass: 'dragging-item', // 拖拽元素样式
                onStart: this.handleStartDrag.bind(this),
                onEnd: this.handleEndDrag.bind(this),
                onAdd: this.handleAdd.bind(this),
                onRemove: this.handleRemove.bind(this),
                onClone: this.handleClone.bind(this),
                onUpdate: this.handleUpdate.bind(this),
                ...this.options,
                group
            });
        },

        /**
         * 给克隆元素添加样式
         * 也就是原地保留的元素样式
         */
        handleClone(event) {
            const cloneElement = event.clone;
            cloneElement.classList.add('drag-clone-item');
        },

        handleStartDrag(event) {
            const element = this.getDragElement(event);
            event.item._underlying_vm_ = cloneDeep(element);
            this.$emit('start', event);
        },

        getDragElement(event) {
            const dragDom = event.item;
            const children = this.$refs.draggableContainer.children;
            const index = Array.from(children).indexOf(dragDom);
            return this.items[index];
        },

        handleEndDrag(event) {
            this.$emit('end', event);
        },

        /**
         * 处理跨列添加节点，移除 sortable.js 直接操作的 dom 元素，适配 vue 数据管理
         * 1. 获取拖拽实际元素
         * 2. 移除 sortable.js 添加的 dom 节点
         * 3. 更新 vue 数据
         */
        handleAdd(event) {
            const element = event.item._underlying_vm_;
            if (element === undefined) {
                return;
            }

            this.removeNode(event.item);
            this.alterItems((items) => items.splice(event.newIndex, 0, element));
        },

        /**
         * 处理跨列删除节点，移除 sortable.js 直接操作的 dom 元素，适配 vue 数据管理
         * 1. 先恢复 dom
         * 2. 移除 clone 节点
         * 3. 更新 vue 数据
         */
        handleRemove(event) {
            this.insertNodeAt(
                this.$refs.draggableContainer,
                event.item,
                event.oldIndex
            );
            this.removeNode(event.clone);
            this.alterItems((items) => items.splice(event.oldIndex, 1));
        },

        /**
         * 处理同列更新，移除 sortable.js 直接操作的 dom 元素，适配 vue 数据管理
         * 1. 移除新增元素
         * 2. 添加原来的元素
         * 3. 更新 vue 数据
         */
        handleUpdate(event) {
            this.removeNode(event.item);
            this.insertNodeAt(event.from, event.item, event.oldIndex);

            this.alterItems(items => {
                const oldItem = items.splice(event.oldIndex, 1)[0];
                items.splice(event.newIndex, 0, oldItem);
            });
        },

        /**
         * 从 DOM 中移除被拖拽的元素节点
         */
        removeNode(node) {
            if (node.parentElement !== null) {
                node.parentElement.removeChild(node);
            }
        },

        /**
         * 在指定位置插入 DOM 节点
         */
        insertNodeAt(fatherNode, node, position) {
            const refNode =
                position === 0
                    ? fatherNode.children[0]
                    : fatherNode.children[position - 1].nextSibling;
            fatherNode.insertBefore(node, refNode);
        },

        /**
         * 更新数据
         */
        alterItems(fn) {
            const items = [...this.items];
            fn(items);
            this.$emit('update:items', items);
        }
    }
};
</script>

<style scoped>
.draggable-container {
    display: flex;
    flex-direction: column;
}

/** 元素选中、拖拽时的样式 */
.drag-item.drag-chosen-item,
.drag-item.dragging-item {
    opacity: 0.8 !important;
    outline: 1px solid #2d8de5;
    background: var(--main-color) !important;
}

/** 隐藏拖拽占位符和原位置元素 */
.drag-item.drag-clone-item,
.drag-item.drag-ghost-item {
    outline: 1px dashed #2d8de5 !important;
}

/deep/ .drag-item.drag-clone-item *,
/deep/ .drag-item.drag-ghost-item * {
    color: transparent !important;
    border-color: transparent !important;
    background: none !important;
    outline: none !important;
}

/deep/ .drag-item.drag-clone-item *::before,
/deep/ .drag-item.drag-clone-item *::after,
/deep/ .drag-item.drag-ghost-item *::before,
/deep/ .drag-item.drag-ghost-item *::after {
    content: none !important;
}

/** 拖拽占位元素样式 */
.drag-item.drag-ghost-item {
    background: rgba(45, 141, 229, 0.29) !important;
}
</style>
