<!--
    统一弹窗组件：
        支持多种弹窗类型和内容展示方式

    @参数说明：modalInfo对象中
        status: 弹窗状态
        width: 自定义弹窗宽度
        title：弹窗标题文字
        modalTitle：弹窗标题（用于h-msg-box-safe的title属性）
        useHeaderSlot：是否使用header插槽（true: 左对齐样式, false: 居中样式）
        type: 弹窗类型 ('success' | 'warning' | 'error' | 'info' | 'custom')
        contentType：内容类型 ('text' | 'table' | 'obj') 或数组 ['text', 'table'] 支持多种内容同时显示
        contentText: 文字内容 （contentType 包含 'text' 时需传）
        columns：表格表头 （contentType 包含 'table' 时需传）
        tableData：表格数据 （contentType 包含 'table' 时需传）
        contentDic：对象展示的字段配置 （contentType 包含 'obj' 时需传）
        contentObj：对象数据 （contentType 包含 'obj' 时需传）
        configKey：弹窗需确认时，抛出 "modal-confirm" 事件 传参configKey作为弹窗唯一标识
        customButtons：自定义按钮配置数组
        iconName：自定义图标名称
        iconColor：自定义图标颜色
        closable：是否显示关闭按钮
        maskClosable：是否允许点击遮罩关闭
        maxHeight：最大高度
        allowCopy：是否允许复制内容
 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :closable="modalData.closable !== undefined ? modalData.closable : false"
            :mask-closable="modalData.maskClosable !== undefined ? modalData.maskClosable : false"
            :width="modalData.width || '450'"
            :title="!modalData.useHeaderSlot ? (modalData.modalTitle || $t('common.tip')) : undefined"
            :maxHeight="modalData.maxHeight || '350'"
            :allowCopy="modalData.allowCopy !== undefined ? modalData.allowCopy : true"
            :canDrag="modalData.canDrag !== undefined ? modalData.canDrag : false"
        >
            <!-- 使用header插槽的样式（左对齐） -->
            <template v-if="modalData.useHeaderSlot" v-slot:header>
                <div class="header-info header-slot">
                    <h-icon
                        :name="getIconName()"
                        :color="getIconColor()"
                        :size="28"
                    ></h-icon>
                    <span class="title-info">{{  getTitle() }}</span>
                </div>
            </template>

            <!-- 不使用header插槽的样式（居中） -->
            <div v-if="!modalData.useHeaderSlot" class="header-info header-body">
                <h-icon
                    :name="getIconName()"
                    :color="getIconColor()"
                    :size="28"
                ></h-icon>
                <p class="title-info">{{  getTitle() }}</p>
            </div>

            <div v-if="modalData.contentType" class="content-body">
                <!-- 文本内容 -->
                <div v-if="modalData.contentType === 'text' || (Array.isArray(modalData.contentType) && modalData.contentType.includes('text'))" class="text-body">
                    {{ modalData.contentText }}
                </div>

                <!-- 表格内容 -->
                <h-table
                    v-if="modalData.contentType === 'table' || (Array.isArray(modalData.contentType) && modalData.contentType.includes('table'))"
                    showTitle
                    :columns="modalData.columns"
                    :data="modalData.tableData"
                    :maxHeight="300"
                    :loading="loading"
                ></h-table>

                <!-- 对象内容 -->
                <div v-if="modalData.contentType === 'obj' || (Array.isArray(modalData.contentType) && modalData.contentType.includes('obj'))" class="obj-body">
                    <p v-for="info in modalData.contentDic" :key="info.key">
                        <span :title="info.title">{{ info.title }}</span>
                        <span :title="modalData.contentObj[info.key]">
                            {{ modalData.contentObj[info.key] }}
                        </span>
                    </p>
                </div>

                <!-- 插入自定义内容 -->
                <slot name="extraContent"></slot>
            </div>

            <template v-slot:footer>
                <div>
                    <!-- 自定义按钮 -->
                    <template v-if="modalData.customButtons && modalData.customButtons.length">
                        <a-button
                            v-for="btn in modalData.customButtons"
                            :key="btn.key"
                            :type="btn.type || 'default'"
                            :loading="btn.loading"
                            @click="handleCustomButtonClick(btn)"
                        >
                            {{ btn.text }}
                        </a-button>
                    </template>

                    <!-- 默认按钮 -->
                    <template v-else>
                        <a-button @click="handleClose">
                            {{ getCloseButtonText() }}
                        </a-button>
                        <a-button
                            v-if="modalData.configKey"
                            type="primary"
                            @click="handleConfirm"
                        >
                            {{ modalData.configKey || $t('common.confirm') }}
                        </a-button>
                    </template>
                </div>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';

export default {
    name: 'UnifiedModal',
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo || {}
        };
    },
    watch: {
        modalInfo: {
            handler(newVal) {
                this.modalData = newVal || {};
            },
            deep: true
        }
    },
    mounted() {
        this.loading = true;
        setTimeout(() => {
            this.loading = false;
        }, 300);
    },
    methods: {
        // 获取图标名称
        getIconName() {
            if (this.modalData.iconName) {
                return this.modalData.iconName;
            }

            const iconMap = {
                success: 't-b-correctinformati',
                warning: 'android-alert',
                error: 'closecircled',
                info: 'prompt',
                custom: 'feedback'
            };

            return iconMap[this.modalData.type] || 'prompt';
        },

        // 获取图标颜色
        getIconColor() {
            if (this.modalData.iconColor) {
                return this.modalData.iconColor;
            }

            const colorMap = {
                success: '#52C41A',
                warning: '#FF9901',
                error: '#F5222D',
                info: 'var(--warning-color)',
                custom: 'var(--warning-color)'
            };

            return colorMap[this.modalData.type] || 'var(--warning-color)';
        },

        // 获取标题
        getTitle() {
            if (this.modalData.title) {
                return this.modalData.title;
            }

            return '';
        },

        // 获取关闭按钮文本
        getCloseButtonText() {
            if (this.modalData.configKey) {
                return this.$t('common.cancel');
            }
            return this.$t('common.close');
        },

        // 处理关闭
        handleClose() {
            this.modalData.status = false;
            this.$emit('modal-close');
        },

        // 处理确认
        handleConfirm() {
            this.$emit('modal-confirm', this.modalData.configKey, this.modalData.configData);
            this.modalData.status = false;
        },

        // 处理自定义按钮点击
        handleCustomButtonClick(btn) {
            this.$emit('custom-button-click', btn.key, btn, this.modalData);
            if (btn.autoClose !== false) {
                this.modalData.status = false;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.header-info {
    font-weight: 500;
}

// header插槽样式（左对齐）
.header-slot {
    display: flex;

    & > .h-icon {
        position: relative;
        top: 2px;
    }

    .title-info {
        line-height: 30px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

// body中的header样式（居中）
.header-body {
    text-align: center;
}

/deep/ .h-modal-header {
    border: none;
}

/deep/ .h-modal-footer {
    border: none;
}

/deep/ .h-modal-body {
    padding: 16px 32px;
}

/deep/ .h-table-row-checked td {
    background: #fff;
}

.content-body {
    .text-body {
        font-size: 12px;
        color: #495060;
        text-align: left;
        line-height: 20px;
        position: relative;
        margin: 0 0 5px;
    }

    .obj-body {
        color: var(--font-color);
        line-height: 30px;
        width: 100%;

        p {
            display: flex;

            span {
                width: 50%;
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}
</style>
