import * as echarts from 'echarts';
import i18n from '@/locales/i18n';

const labelColor = '#fff'; // 标签文字颜色
const axisColor = '#3e3f3f'; // 坐标轴颜色
const textColor = '#777';
const colors = ['#4A92FF', '#5AD8A6', '#FDDD60', '#FF6E76', '#58D9F9', '#04C091', '#FF8A45', '#8D48E3', '#DD79FF', '#534AFF', '#C2C8D5', '#FF89A7', '#FFDEAD'];
// 暗底chart标准样式
export const xyOption = {
    backgroundColor: '#262b40',
    color: colors,
    title: {
        text: '',
        left: 'center',
        top: 10,
        textStyle: {
            color: labelColor,
            fontSize: 14
        }
    },
    grid: {
        left: 40,
        right: 30,
        bottom: 20,
        top: 50,
        containLabel: true
    },
    // 提示框
    tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(88,94,106,0.40)',
        borderColor: 'rgba(88,94,106,0.40)',
        textStyle: {
            color: labelColor,
            fontSize: 12
        }
    },
    legend: {
        textStyle: {
            color: labelColor,
            fontSize: 11
        },
        // 图标距离容器顶部高度
        top: 0,
        type: 'scroll',
        pageIconColor: '#2d8de5',
        pageIconInactiveColor: '#2f4554',
        pageTextStyle: {
            color: labelColor
        }
    },
    xAxis: {
        type: 'category',
        boundaryGap: false, // x轴两端留白
        axisLine: {
            show: true,
            lineStyle: {
                color: axisColor
            }
        },
        axisTick: {
            show: false // 是否显示坐标轴刻度
        },
        axisLabel: {
            color: labelColor,
            textStyle: {
                width: '0'
            }
        },
        data: []
    },
    yAxis: [
        {
            type: 'value',
            axisLine: {
                show: true,
                lineStyle: {
                    color: axisColor
                }
            },
            nameTextStyle: {
                color: labelColor
            },
            axisLabel: {
                color: labelColor
            },
            splitLine: {
                lineStyle: {
                    color: axisColor
                }
            }
        },
        {
            show: false,
            type: 'value',
            axisLine: {
                show: true,
                lineStyle: {
                    color: axisColor
                }
            },
            nameTextStyle: {
                color: labelColor
            },
            axisLabel: {
                color: labelColor
            },
            splitLine: {
                lineStyle: {
                    color: axisColor
                }
            }
        }
    ],
    toolbox: {
        right: 10
    },
    series: []
};
export const pieOption = {
    backgroundColor: '#262b40',
    color: colors,
    title: {
        text: '',
        left: 'center',
        top: 10,
        textStyle: {
            color: labelColor,
            fontSize: 14
        }
    },
    tooltip: {
        trigger: 'item'
    },
    // legend: {
    //     icon: 'circle',
    //     orient: 'vertical',
    //     left: 'left',
    //     top: 30,
    //     textStyle: {
    //         color: labelColor,
    //         fontSize: 11
    //     },
    //     type: 'scroll',
    //     pageIconColor: '#2d8de5',
    //     pageIconInactiveColor: '#2f4554',
    //     pageTextStyle: {
    //         color: labelColor
    //     }
    // },
    series: [{
        type: 'pie',
        radius: '60%',
        itemStyle: {
            borderRadius: 2,
            borderColor: '#262b40',
            borderWidth: 1
        },
        label: {
            show: true,
            formatter: function (params) {
                return params?.data?.rate ? `${params.name} (${params.data.rate})` : params.name;
            }
        },
        labelLine: {
            show: true
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    }],
    emphasis: {
        itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
    }
};
export const barOption = {
    xAxis: {
        type: 'value',
        axisLine: {
            lineStyle: {
                color: labelColor
            }
        },
        splitLine: {
            lineStyle: {
                color: axisColor
            }
        }
    },
    yAxis: {
        type: 'category',
        inverse: true, // 反向排序
        data: [],
        axisLine: {
            show: true,
            lineStyle: {
                color: labelColor
            }
        }
    }
};

// 白底option配置
export const whiteXyOption = {
    backgroundColor: '#fff',
    color: colors,
    title: {
        text: '',
        left: 'center',
        top: 10,
        textStyle: {
            fontSize: 14
        }
    },
    grid: {
        left: 40,
        right: 30,
        bottom: 20,
        top: 50,
        containLabel: true
    },
    legend: {
        type: 'scroll'
    },
    tooltip: {
        trigger: 'axis'
    },
    xAxis: {
        type: 'category',
        boundaryGap: false, // x轴两端留白
        data: []
    },
    yAxis: [
        {
            type: 'value'
        }
    ],
    series: []
};

// 无数据option配置
export const noDataOption = {
    title: {
        text: i18n.t('pages.common.noData'),
        x: 'center',
        y: 'center',
        textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: textColor
        }
    }
};

// 甘特图option配置
const startTime = 0;
function renderItem(params, api) {
    const categoryIndex = api.value(0);
    const start = api.coord([api.value(1), categoryIndex]);
    const end = api.coord([api.value(2), categoryIndex]);
    const duration = api.value(3);
    const height = api.size([0, 1])[1] * 0.4;
    const maxHeight = height > 20 ? 20 : height;
    const rectShape = echarts.graphic.clipRectByRect(
        {
            x: start[0],
            y: start[1] - maxHeight / 2,
            width: end[0] - start[0],
            height: maxHeight
        },
        {
            x: params.coordSys.x,
            y: params.coordSys.y,
            width: params.coordSys.width,
            height: params.coordSys.height
        }
    );
    return (
        rectShape && {
            type: 'rect',
            transition: ['shape'],
            shape: rectShape,
            style: api.style({
                fill: '#81D3F8',
                text: duration + ' μs',
                textFill: '#111'
            })
        }
    );
}
export const ganttOption = {
    tooltip: {
        formatter: function (params) {
            return params.marker + params.name + ': ' + params.value[3] + ' μs';
        }
    },
    title: {
        text: ''
    },
    grid: {
        top: 40,
        left: 80
    },
    // dataZoom: [
    //     {
    //         type: 'slider',
    //         filterMode: 'weakFilter',
    //         showDataShadow: false,
    //         top: 0,
    //         labelFormatter: ''
    //     },
    // ],
    xAxis: {
        position: 'top',
        min: startTime,
        scale: true,
        axisLabel: {
            formatter: function (val) {
                return Math.max(0, val - startTime) + ' μs';
            }
        }
    },
    yAxis: {
        axisTick: { show: false },
        axisLine: { show: false },
        data: [],
        inverse: true
    },
    series: [
        {
            type: 'custom',
            renderItem: renderItem,
            encode: {
                x: [1, 2],
                y: 0
            },
            data: []
        }
    ]
};

// 机房告警分布环形图配置
export const hoopOption = {
    color: ['#4A92FF', '#5AD8A6', '#FDDD60', '#FF6E76', '#58D9F9', '#04C091', '#FF8A45', '#8D48E3', '#DD79FF'],
    legend: {
        selectedMode: false,
        icon: 'circle',
        orient: 'vertical',
        top: 'center',
        left: '50%',
        textStyle: {
            align: 'left',
            verticalAlign: 'middle',
            rich: {
                name: {
                    color: '#CACFD4',
                    fontSize: 12,
                    width: 115
                },
                rate: {
                    color: '#fff',
                    fontSize: 12,
                    fontWeight: '600',
                    width: 40,
                    align: 'right'
                }
            }
        }
    },
    series: [
        {
            name: '预警数量',
            type: 'pie',
            radius: ['60%', '85%'],
            center: ['25%', '50%'],
            avoidLabelOverlap: false,
            label: {
                show: true,
                position: 'center',
                formatter: function() {
                    return '{val|' + 0 + '}\n{name|预警数量}';
                },
                rich: {
                    name: {
                        fontSize: 12,
                        color: '#CACFD4',
                        padding: [10, 0, 0, 0]
                    },
                    val: {
                        fontSize: 28,
                        fontWeight: '600',
                        color: '#fff',
                        padding: [15, 0, 0, 0]
                    }
                }
            },
            labelLine: {
                show: false
            },
            data: []
        }
    ]
};

// 机房拓扑地图配置
export const mapTopoOption = {
    // 地图配置
    geo: {
        map: 'china',
        zoom: 1.2, // 视觉比例大小,1.2即为原有大小的1.2倍
        roam: true, // 是否开启鼠标缩放和平移漫游。默认不开启。可以不用设置,如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。
        label: {
            // 鼠标放上去的样式
            emphasis: {
                show: true,
                color: '#fff'
            }
        },
        // 地图区域的样式设置
        itemStyle: {
            normal: {
                color: 'rgba(86, 100, 124)', // 地图背景色
                borderColor: '#303133', // 省市边界线00fcff 516a89
                borderWidth: 0.5
            },
            // 鼠标放上去高亮的样式
            emphasis: {
                areaColor: 'rgba(228, 228, 228, .5)' // 悬浮背景
            }
        }
    },
    tooltip: {
        trigger: 'item',
        enterable: true, // 鼠标是否可进入提示框浮层中
        backgroundColor: 'rgba(88,94,106,0.70)',
        textStyle: {
            color: '#fff'
        },
        triggerOn: 'click',
        formatter: function(params, ticket, callback) {
            if (params.seriesType === 'scatter') {
                const liStyle = 'white-space: normal; padding: 0 0 8px 8px; line-height: 12px;';
                const data = params.data;
                // 根据业务自己拓展要显示的内容
                let html = '';
                html += `<div style="font-size: 12px; user-select: text;">
                    <div style="border-bottom: 1px solid #ccc; padding-bottom: 4px; font-weight: 500;">机房信息</div>
                    <div style="display: flex; margin-top: 10px;">
                            <ul>
                                <li style='${liStyle}'><span style="color: #cacfd4;">机房名称:</span> ${params.name || '-'}</li>
                                <li style='${liStyle}'><span style="color: #cacfd4;">区域: </span> ${data?.area || '-'}</li>
                                <li style='${liStyle}'><span style="color: #cacfd4;">状态: </span> ${data?.status || '-'}</li>
                                <li style='${liStyle}'><span style="color: #cacfd4;">当日告警总数: </span> ${data?.count ?? '-'}</li>
                                <li style='${liStyle}'><span style="color: #cacfd4;">最近一次告警时间:</span> ${data?.lastAlertTime || '-'}</li>
                                <li style='${liStyle}'><span style="color: #cacfd4;">告警详情:</span> <span onclick="roomTopoJump('alert', '${params.name}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>
                                <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">集群拓扑:</span> <span onclick="roomTopoJump('topo', '${params.name}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>
                            </ul>
                    </div>
                    </div>`;
                return html;
            }
        }
    },
    visualMap: {
        left: 26,
        bottom: 40,
        showLabel: true,
        seriesIndex: 0,
        text: ['机房数量'],
        textStyle: {
            color: '#fff'
        },
        pieces: [
            {
                gt: 10,
                label: '> 10 个',
                color: '#4A92FF'
            },
            {
                gte: 4,
                lte: 10,
                label: '4 - 10 个',
                color: '#4e95df'
            },
            {
                gte: 1,
                lte: 3,
                label: '1 - 3 个',
                color: '#6E96D5'
            },
            {
                value: 0,
                color: 'rgba(86, 100, 124)'
            }
        ]
    },
    series: [
        {
            name: '机房数量',
            type: 'map',
            geoIndex: 0,
            select: {
                disabled: true
            },
            data: []
        },
        {
            type: 'scatter',
            coordinateSystem: 'geo',
            symbol: 'path://M512 118.8864c-155.648 0-282.2144 128.6144-282.2144 286.6176 0 53.248 12.8 111.616 38.0928 173.3632 20.0704 49.0496 48.128 100.4544 83.2512 152.8832 59.5968 88.8832 120.9344 152.3712 127.6928 159.3344 8.8064 9.0112 20.5824 14.0288 33.1776 14.0288 12.5952 0 24.3712-5.0176 33.1776-14.0288 6.8608-6.9632 68.096-70.4512 127.6928-159.3344 35.1232-52.4288 63.1808-103.8336 83.2512-152.8832C781.4144 517.12 794.2144 458.752 794.2144 405.504c0-158.0032-126.5664-286.6176-282.2144-286.6176z m0 374.1696c-62.5664 0-113.4592-50.8928-113.4592-113.4592S449.4336 266.24 512 266.24s113.4592 50.8928 113.4592 113.4592S574.5664 493.056 512 493.056z', // 定位图标样式
            symbolSize: [15, 20],
            symbolOffset: [0, '-50%'],
            itemStyle: {
                normal: {
                    show: true,
                    color: '#64FFFC'
                }
            },
            label: {
                normal: {
                    color: '#64FFFC',
                    backgroundColor: 'rgba(88,94,106,0.70)',
                    borderColor: 'transparent',
                    borderWidth: 2,
                    lineHeight: 25,
                    padding: [1, 4],
                    show: true,
                    position: 'right',
                    formatter: function(params) {
                        const status = params?.data?.status;
                        return status ? '{' + params?.data?.status + '|' + params.name + '}' : params.name;
                    },
                    rich: {
                        runing: {
                            color: '#46DBA0'
                        },
                        warning: {
                            color: '#FFF06B'
                        },
                        error: {
                            color: '#FF3C3C'
                        }
                    }
                },
                emphasis: {
                    show: true,
                    color: '#BDFFF7'
                }
            },
            labelLayout: function(params) {
                return {
                    x: params.rect.x + 20,
                    y: params.rect.y - 10,
                    moveOverlap: 'shiftY'
                };
            },
            data: []
        },
        {
            type: 'lines',
            zlevel: 2,
            // 线特效配置
            effect: {
                show: true,
                period: 4, // 箭头指向速度，值越小速度越快
                trailLength: 0.05, // 特效尾迹长度[0,1]值越大，尾迹越长重
                symbol: 'arrow', // 标记类型
                symbolSize: 6
            },
            lineStyle: {
                normal: {
                    width: 1, // 尾迹线条宽度
                    opacity: 0.4,
                    curveness: 0.3, // 尾迹线条曲直度
                    color: '#64FFFC'
                }
            },
            data: []
        }
    ]
};
