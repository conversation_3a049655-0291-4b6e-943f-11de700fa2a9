<template>
    <div class="main">
        <!-- 头部 -->
        <table-sql-top
            ref="manage-top"
            class="title-line"
            :disabled="loadRuning"
            @connect-database="connectDatabase"
            @clear-data="clearData">
        </table-sql-top>
        <a-loading v-if="loading" style="z-index: 101;"></a-loading>
        <!-- 内存表管理 -->
        <h-tabs
            v-if="isShowTable"
            ref="productBox"
            v-model="tabName"
            class="product-box">
            <h-tab-pane label="SQL" name="SQL">
                <menu-layout
                    ref="menu"
                    customMenu
                    titleAttribute="tableName"
                    placeholder="查询内存表">
                    <template v-slot:menu>
                        <div v-show="isOpen" class="menu-box">
                            <h-input
                                v-model="searchText"
                                class="search-bar"
                                placeholder="输入表名过滤"
                                icon="search"></h-input>
                            <h-tree
                                ref="tree"
                                class="menu-tree"
                                :data="tableTreeList"
                                :load-data="handleLoadChildData">
                            </h-tree>
                            <h-checkbox
                                v-model="isOnlyRedo"
                                :label="true"
                                style="margin: 12px 0 0 10px;">
                                <span style="color: #fff;">仅显示"不支持主备同步"表</span>
                            </h-checkbox>
                        </div>
                    </template>
                    <template v-slot:right>
                        <sql-edit
                            v-show="sqlEditVisible"
                            ref="sqlEditor"
                            :tableList="tableTreeList.map(v => v.originTableName)"
                            :isChange="true"
                            :style="{height: `${lastEditHeight}px`}"
                            @query="getTableMetadata"
                            @code-change="onChangehandle" />
                        <h-row class="sql-resizer">
                            <div
                                class="y-resizer"
                                @mousedown="mouseDown">
                            </div>
                            <span
                                class="cate-switch"
                                @click="changeShowHidden">
                                <h-icon
                                    :name="sqlEditVisible ? 'packup' : 'unfold'"
                                    size='14'
                                    color="#cacfd4">
                                </h-icon>
                            </span>
                        </h-row>
                        <sql-result
                            ref="sqlResult"
                            :resultList="resultList"
                            :clusterRoles="clusterRoles"
                            :style="sqlEditVisible ?
                                { height: `calc(100% - ${(editHeight + 20)}px)` } :
                                { height: '100%' }"
                            :height="tableHeight"
                            :getTableMetaData="getTableMetaDta"
                            @callSetLoginInfo="callSetLoginInfo"
                            @handlePageChange="handlePageChange"
                            @errorInfo="showErrorInfoModal"
                        />
                    </template>
                </menu-layout>
            </h-tab-pane>
            <h-tab-pane label="SCHEMA" name="SCHEMA">
                <menu-layout
                    ref="menu2"
                    :menuList="schemaList"
                    :useRegExpSearch="false"
                    titleAttribute="generateName"
                    placeholder="输入表名过滤"
                    showSearch
                    @check-menu="checkTableBySchema">
                    <template v-slot:menuFooter>
                        <div style="border-top: 1px solid var(--border-color);">
                            <h-checkbox
                                v-model="isOnlyRedo"
                                :label="true"
                                style="margin: 12px 0 0 10px; position: absolute;">
                                <span style="color: #fff;">仅显示"不支持主备同步"表</span>
                            </h-checkbox>
                        </div>
                    </template>
                    <template v-slot:right>
                        <h-tabs>
                            <h-tab-pane
                                label="索引"
                                name="索引">
                                <a-table
                                    :columns="indexColumns"
                                    :tableData="indexData"
                                    showTitle
                                    canDrag
                                    :hasPage="false"
                                    style="padding: 0 4px;"
                                    border></a-table>
                            </h-tab-pane>
                            <h-tab-pane
                                label="关联关系"
                                name="关联关系">
                                <a-table
                                    :columns="relationColumns"
                                    :tableData="relationData"
                                    showTitle
                                    canDrag
                                    :hasPage="false"
                                    style="padding: 0 4px;"
                                    border></a-table>
                            </h-tab-pane>
                        </h-tabs>
                    </template>
                </menu-layout>
            </h-tab-pane>
            <template v-slot:extra>
                <div style="position: fixed; display: flex; right: 175px; top: 6px;">
                    <a-button
                        v-show="tabName === 'SQL'"
                        type="primary"
                        :loading="loadRuning"
                        style="margin-right: 6px;"
                        @click="handleSqlRun">执行</a-button>
                    <a-button
                        v-if="isShowTable && tabName === 'SQL'"
                        type="dark"
                        style="margin-right: 6px;"
                        :disabled="loadRuning"
                        @click="showSqlFileModal">导入SQL</a-button>
                    <a-button
                        v-show="tabName === 'SQL'"
                        type="dark"
                        :disabled="loadRuning"
                        @click="handleHistoryDrawer">历史SQL</a-button>

                    <!--分隔符-->
                    <div
                        v-show="tabName === 'SQL'"
                        class="input-line"
                        style="margin: 0 6px;"></div>

                    <h-input
                        v-model.number="timeout"
                        :disabled="loadRuning"
                        type="int"
                        titleShow
                        specialFilter
                        :specialLength="10"
                        :specialDecimal="0"
                        style="width: 115px; margin-right: 6px;">
                        <span
                            slot="prepend"
                            style="background: var(--button-bg-color);">超时:</span>
                        <span slot="append">秒</span>
                    </h-input>
                    <a-button
                        type="dark"
                        icon="help"
                        title="使用说明"
                        :disabled="loadRuning"
                        style="padding: 6px 10px;"
                        @click="() => {instructInfo.status = true;}"></a-button>
                </div>
            </template>
        </h-tabs>
        <template v-else>
            <no-data text="请选择并连接应用节点！" />
        </template>
        <!-- sql历史执行记录 -->
        <history-list-modal
            v-if="historyInfo.status"
            :modalInfo="historyInfo"
            :historyList="historyList"
            @addSqlToEdit="addSqlToEdit" />
        <instruct-modal v-if="instructInfo.status" :modalInfo="instructInfo" />
        <!-- sql文件执行 -->
        <sql-file-run-modal
            v-if="sqlInfo.status"
            :modalInfo="sqlInfo"
            @errorInfo="showErrorInfoModal" />
        <!-- sql错误节点执行原因 -->
        <sql-error-info-modal
            v-if="sqlErrorInfo.status"
            :modalInfo="sqlErrorInfo"/>
        <!-- sql执行确认 -->
        <perform-confirm-modal
            v-if="performConfirmInfo.status"
            :modalInfo="performConfirmInfo"
            @handleSqlRun="handleSqlRun" />
    </div>
</template>

<script>
import _ from 'lodash';
import { MDB_NO_LOGIN } from '@/config/errorCode';
import { getSpanWidth, getByteSize, tryGetSqlSelectAst, tryGetSqlPretix } from '@/utils/utils';
import { getTables, getTableMetadata } from '@/api/httpApi';
import { getMemoryDataBySql, getTableMetaDta, getMemorySqlRouter } from '@/api/memoryApi';
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import tableSqlTop from '@/components/sqlTable/tableSqlTop';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import sqlEdit from '@/components/sqlTable/sqlEdit/sqlEdit';
import sqlResult from '@/components/sqlTable/sqlResult/sqlResult';
import sqlFileRunModal from '@/components/sqlTable/modal/sqlFileRunModal.vue';
import instructModal from '@/components/sqlTable/modal/instructModal.vue';
import historyListModal from '@/components/sqlTable/modal/historyListModalNew.vue';
import sqlErrorInfoModal from '@/components/sqlTable/modal/sqlErrorInfoModal';
import performConfirmModal from '@/components/sqlTable/modal/performConfirmModal';

// 连接数据库
export default {
    components: {
        aTable,
        aLoading,
        menuLayout,
        sqlResult,
        noData,
        sqlEdit,
        aButton,
        tableSqlTop,
        historyListModal,
        instructModal,
        sqlFileRunModal,
        sqlErrorInfoModal,
        performConfirmModal
    },
    data() {
        return {
            productInstNo: '',
            endpointId: '',
            loading: false,
            loadRuning: false,
            isShowTable: false,
            visible: false, // 历史侧边栏
            tableTreeList: [],
            tableList: [],
            resultList: [],
            instance: [],
            sqlTempList: [], // sql模板
            tabName: 'SQL',
            newTabName: '',
            tabList: [],
            searchText: '',
            isOpen: true,
            historyList: [],
            historyInfo: {
                status: false
            },
            instructInfo: {
                status: false
            },
            sqlInfo: {
                status: false,
                endpointId: '',
                instance: [],
                timeout: ''
            },
            sqlErrorInfo: {
                status: false
            },
            // 索引表
            indexColumns: [
                {
                    title: '索引名',
                    key: 'indexName',
                    ellipsis: true
                },
                {
                    title: '索引类型',
                    key: 'indexType',
                    ellipsis: true
                },
                {
                    title: '索引字段',
                    key: 'filedName',
                    ellipsis: true
                }
            ],
            indexData: [],
            // 关联关系表
            relationColumns: [
                {
                    title: '关联对象',
                    key: 'relationObj',
                    ellipsis: true
                }, {
                    title: '关联对象身份',
                    key: 'identity',
                    ellipsis: true
                }, {
                    title: '关联类型',
                    key: 'relationType',
                    render: (h, params) => {
                        return h('span', params.row.relationType === 2 ? '包含' : '关联');
                    }
                }, {
                    title: '关联关系',
                    key: 'relation',
                    render: (h, params) => {
                        return h('span', params.row.relation === 1 ? '1:1' : '1:N');
                    }
                }, {
                    title: '关联字段',
                    key: 'relationField',
                    ellipsis: true
                }, {
                    title: '关联索引',
                    key: 'relationIndex',
                    ellipsis: true
                }
            ],
            performConfirmInfo: {
                status: false,
                sqlList: [],
                instances: []
            },
            relationData: [],
            timeout: 10,
            maxTimeWait: 10 * 60,
            isOnlyRedo: false,
            isFirstRender: false,
            sqlEditVisible: true,
            isFetching: false,
            // sqlEdit 拖动相关
            startY: 0,
            editHeight: 200,
            lastEditHeight: 200,
            clusterRoles: {},
            schemaList: [],
            serviceCode: '',
            instanceCount: {}
        };
    },
    async mounted() {
        this.loading = true;
        try {
            const productInstNo = localStorage.getItem('productInstNo') || '';
            await this.$refs['manage-top'].init(productInstNo);
            this.$nextTick(() => {
                this.clusterRoles = this.$refs['manage-top'].clusterRoles;
            });
            this.isFirstRender = true;
        } finally {
            this.loading = false;
        }
        window.addEventListener('resize', this.resize);
    },
    computed: {
        tableHeight: function() {
            if (this.sqlEditVisible) {
                return (this.$refs.productBox?.$el?.clientHeight || 0) - this.editHeight - 140;
            }
            return (this.$refs.productBox?.$el?.clientHeight || 0) - 140 ;
        }
    },
    methods: {
        // 重置tableHeight
        resize() {
            const h = this.editHeight;
            this.editHeight = 0;
            this.$nextTick(() => {
                this.editHeight = h;
            });
        },
        // 创建SQL历史记录项
        createSqlHistoryItem({ time, sql, type, instance, product }) {
            const baseItem = {
                time,
                sql,
                type,
                productInstNo: product?.productInstNo,
                productName: product?.productName
            };

            if (type === 'service') {
                return {
                    ...baseItem,
                    serviceName: instance?.serviceName,
                    badge: instance?.badge,
                    label: instance?.label
                };
            } else if (type === 'cluster') {
                return {
                    ...baseItem,
                    serviceName: instance?.clusterName,
                    badge: instance?.badge,
                    label: instance?.label
                };
            } else {
                return {
                    ...baseItem,
                    serviceName: instance?.label,
                    badge: instance?.badge,
                    label: instance?.label
                };
            }
        },
        // 设置SQL执行参数
        setSqlExecutionParams(param, type, instances) {
            if (type === 'service') {
                param.instanceIds = instances.map(item => item.value).join();
                param.serviceCode = this.serviceCode;
            } else {
                param.instanceId = instances?.[0]?.value;
            }
        },
        // 获取实例名称
        getInstanceName(type, instance) {
            if (type === 'service') {
                return instance?.serviceName;
            } else if (type === 'cluster') {
                return instance?.clusterName;
            } else {
                return instance?.label;
            }
        },
        // 获取实例ID
        getInstanceId(type, instance) {
            return type === 'service' ? instance?.serviceKey : instance?.value;
        },
        // 清理数据
        clearData() {
            this.tableTreeList = [];
            this.tableList = [];
            this.tabName = 'SQL';
            this.tabList = [];
            this.resultList = [];
            this.searchText = '';
            this.serviceCode = '';
            this.instanceErrorMsg = [];
            this.isOnlyRedo = false;
            this.isShowTable = false;
        },
        // 点击索引菜单
        async checkTableByIndex(e) {
            let list = [];
            const res = await this.getTableMetaDta(e);
            list = res.indices || [];
            this.indexData = list;
        },
        // 点击关联
        async checkTableBySchema(e) {
            const res = await this.getTableMetaDta(e);
            this.indexData = res.indices || [];
            this.relationData = res.relationIndices || [];
        },
        // 获取表格源信息
        async getTableMetaDta(e) {
            let data;
            const param = {
                tableName: e.tableName,
                endpointId: this.endpointId,
                instanceId: this?.instance?.[1]?.value  || this.instance?.[0]?.value,
                productInstNo: localStorage.getItem('productInstNo')
            };
            try {
                const res = await getTableMetaDta(param);
                if (res.code === MDB_NO_LOGIN){
                    this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
                    return;
                }
                this.$refs?.['manage-top'] && this.$refs['manage-top'].updateToken(res?.headers?.authorization);
                data = res;
            } catch (error) {
                console.error(error);
            }
            return data;
        },
        /**
         * 打开登录弹窗
         */
        callSetLoginInfo() {
            this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
        },
        // 添加sql查询标签
        handleTabsAdd() {
            this.$hMessage.confirm({
                title: '请输入SQL标签名',
                onOk: () => {
                    if (this.newTabName === '') {
                        this.$hMessage.error('标签名不得为空');
                        return;
                    } else if (this.tabList.indexOf(this.newTabName) > 0) {
                        this.$hMessage.error('当前标签名已存在');
                        return;
                    }
                    this.tabList.push(this.newTabName);
                },
                render: (h) => {
                    return h('Input', {
                        props: {
                            value: this.newTabName,
                            autofocus: true,
                            placeholder: '请输入标签名'
                        },
                        on: {
                            input: val => {
                                this.newTabName = val;
                            }
                        }
                    });
                }
            });
        },
        // 连接内存表
        async connectDatabase(param) {
            this.loading = true;
            this.isShowTable = false;
            this.indexData = [];
            this.relationData = [];
            this.resultList = [];
            this.searchText = '';
            this.isOnlyRedo = false;
            this.clusterRoles = this.$refs['manage-top'].clusterRoles;
            if (param.type === 'service') {
                this.serviceCode = param?.instance?.[0]?.serviceKey;
            }
            if (param.instance?.length) {
                this.instance = param.instance;
                try {
                    const data = {
                        productInstNo: param?.productInstNo,
                        endpointId: param?.endpointId
                    };
                    if (param.type === 'service') {
                        data.serviceCode = this.serviceCode;
                        const instances =  this.getInstancesByRoute('SELECT', param.type);
                        data.instanceIds = instances.map(item => item.value).join();
                        if (!data.instanceIds.length) {
                            this.$hMessage.warning({
                                content: '当前服务在此路由策略下无可执行节点，请重新选择',
                                duration: 5
                            });
                            this.loading = false;
                            return;
                        }
                    } else {
                        data.instanceId = param?.instance?.[1]?.value || param?.instance?.[0]?.value;
                        data.instanceName = param?.instance?.[1]?.label || param?.instance?.[0]?.label;
                    }

                    // 调用接口获取库名和表列表
                    await this.getTableLists(data);
                    this.endpointId = param.endpointId;
                    this.isShowTable = Boolean(this.tableList.length);
                } catch (error) {
                    console.error(error);
                }
            }

            if (this.isShowTable){
                this.$nextTick(() => {
                    if (this.isFirstRender && this.$route.query?.sqlDoc) {
                        this.$refs['sqlEditor'].setSqlCode(this.$route.query.sqlDoc);
                        this.handleSqlRun([this.$route.query?.sqlDoc]);
                        this.isFirstRender = false;
                    } else if (sessionStorage.getItem('apm.sqlTableCode')){
                        this.$refs['sqlEditor'].setSqlCode(sessionStorage.getItem('apm.sqlTableCode'));
                    } else {
                        this.$refs['sqlEditor'].setSqlCode('');
                    }
                });

            }

            this.loading = false;
        },
        // 编辑器code change
        onChangehandle(sqlCode){
            const size = getByteSize(sqlCode);
            if (size < 1024 * 1024){
                sessionStorage.setItem('apm.sqlTableCode', sqlCode);
            } else {
                this.$hMessage.info('编辑器内容超过1M不支持暂存!');
            }
        },
        // 获取内存表列表
        async getTableLists(param) {
            if (!param.endpointId) return;
            this.tableTreeList = [];
            this.tableList = [];
            try {
                const res = await getTables(param);
                if (res.code === MDB_NO_LOGIN){
                    this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
                    return;
                }
                if (!res.errcode) {
                    if (Array.isArray(res.tableInfos)) {
                        this.tableList = res.tableInfos.map(ele => ({
                            ...ele,
                            generateName: ele.tableChineseName ? `${ele.tableChineseName}(${ele.tableName})` : ele.tableName
                        }));
                        res.tableInfos.forEach(ele => {
                            const tableName = ele.tableChineseName ? `${ele.tableChineseName}(${ele.tableName})` : ele.tableName;
                            const instances = Array.isArray(ele.instances) ? ele.instances : [];
                            this.tableTreeList.push({
                                title: tableName,
                                originTableName: ele.tableName,
                                hasWriteRedo: ele.hasWriteRedo,
                                sharding: Boolean(ele.sharding),
                                instances,
                                loading: false,
                                children: [],
                                autoLoad: false,
                                render: (h, { root, node, data }) => {
                                    return h(
                                        'div',
                                        {
                                            style: {
                                                width: 'calc(100% - 40px)',
                                                height: '40px',
                                                position: 'absolute',
                                                left: '20px',
                                                top: 0,
                                                color: '#fff',
                                                cursor: 'pointer'
                                            },
                                            on: {
                                                click: () => {
                                                    this.handleTableClick([], data);
                                                }
                                            }
                                        },
                                        [
                                            h('p', {
                                                style: {
                                                    display: 'flex',
                                                    height: '24px',
                                                    fontSize: '13px',
                                                    alignItems: 'center'
                                                }
                                            },
                                            [
                                                h('span', {}, ele.tableChineseName || '-'),
                                                h('icon',
                                                    {
                                                        props: {
                                                            name: 'max',
                                                            size: 12
                                                        },
                                                        style: {
                                                            marginLeft: '5px',
                                                            display: ele.sharding ? '' : 'none'
                                                        }
                                                    }
                                                )
                                            ]),
                                            h('p', {
                                                style: {
                                                    lineHeight: '16px',
                                                    fontSize: '12px',
                                                    color: '#9296A1',
                                                    paddingLeft: '1px'
                                                }
                                            }, ele.tableName),
                                            h('poptip', {
                                                props: {
                                                    title: '此分片表在以下主节点中存在',
                                                    transfer: true,
                                                    trigger: 'hover',
                                                    customTransferClassName: 'apm-poptip monitor-poptip',
                                                    placement: 'right'
                                                },
                                                style: {
                                                    position: 'absolute',
                                                    color: 'var(--link-color)',
                                                    right: '-15px',
                                                    top: '12px',
                                                    display: instances?.length ? 'inline-bolck' : 'none'
                                                }
                                            },
                                            [
                                                h('span', {
                                                    style: {
                                                        marginLeft: '10px'
                                                    }
                                                }, instances.length),
                                                h(
                                                    'div',
                                                    {
                                                        slot: 'content',
                                                        class: 'pop-content',
                                                        style: {
                                                            'max-width': '300px'
                                                        }
                                                    },
                                                    [h('p',  {
                                                        style: {
                                                            'white-space': 'normal'
                                                        }
                                                    },
                                                         (ele?.instances || []).map(o => o?.instanceNo)?.join(','))]
                                                )
                                            ])
                                        ]
                                    );
                                }
                            });
                        });
                        this.schemaList = this.tableList;
                    }
                }
            } catch (err) {
                console.error(err);
            }
        },
        // menu懒加载子节点
        handleLoadChildData(item, callback) {
            this.getTableMetadata(item.originTableName).then(fields => {
                const fieldList = [];
                fields.forEach(ele => {
                    fieldList.push({
                        title: `${ele.type}${ele.size > 1 ? `[${ele.size}]` : ''} ${ele.name}`
                    });
                });
                callback(fieldList);
            });
        },
        // 获取表字段名
        async getTableMetadata(tableName, callback) {
            const param = {
                tableName: tableName,
                endpointId: this.endpointId,
                instanceId: this.instance?.[1]?.value || this.instance?.[0]?.value,
                productInstNo: localStorage.getItem('productInstNo')
            };
            const res = await getTableMetadata(param);
            if (res.code === MDB_NO_LOGIN){
                this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
                return [];
            }
            if (callback) callback(res.fields || []);
            return res.fields || [];
        },
        // 过滤树菜单字段
        treeFilterMethod(value) {
            const nodeList = this.$refs['tree'].getAllNodesState();
            Array.isArray(nodeList) && nodeList.forEach(item => {
                let hiddenStu = this.isOnlyRedo ? item.node?.hasWriteRedo : false;
                if (value && !hiddenStu) {
                    const title = (item.node.title || '').toLowerCase();
                    hiddenStu = !title.includes((value || '').toLowerCase());
                }
                this.$set(item.node, 'hidden', hiddenStu);
            });
        },
        // 打开历史记录侧边栏
        handleHistoryDrawer() {
            const history = localStorage.getItem('HISTORY_SQLS_NEW');
            this.historyList = history ? JSON.parse(history) : [];
            this.historyInfo.status = true;
        },
        // 默认SELECT添加limit
        addLimitToSQL(sql) {
            let newSql = '';
            const selectPattern = /\bSELECT\b/i;
            const limitPattern = /\bLIMIT\b/i;
            const list = sql.split(';');
            if (list[0].trim()) {
                if (!selectPattern.test(list[0]) || limitPattern.test(list[0])) {
                    newSql = list[0] + ';';
                } else if (selectPattern.test(list[0])) {
                    newSql = list[0] + ';';
                }
            }
            return newSql;
        },
        // 根据路由规则判定instances
        getInstancesByRoute(pretix, type) {
            let instances = [...this.instance]; // 默认节点
            if (type === 'service' || type === 'cluster') {
                // 判断路由模式---集群、服务
                const sqlRoute = localStorage.getItem('apm.mdbsql.sqlRoute') ? localStorage.getItem('apm.mdbsql.sqlRoute') : 'onlyMaster';
                if (sqlRoute === 'first') {
                    if (pretix?.toUpperCase() === 'SELECT') {
                        instances = instances.filter(o => !o.badge);
                    } else if (pretix?.toUpperCase() === 'UPDATE' || pretix?.toUpperCase() === 'DELETE' || pretix?.toUpperCase() === 'INSERT'){
                        instances = instances.filter(o => o.badge);
                    }
                } else if (sqlRoute === 'onlyMaster') {
                    instances = instances.filter(o => o.badge);
                }
            }
            return instances;
        },
        // 服务模式下获取sql路由节点
        async getMemorySqlRouter(sqlList) {
            const sqlData = [];
            const routes = [];
            sqlList.forEach(ele => {
                // SQL执行，APM 自动识别用户SQL是读SQL：SELECT，还是写操作集群中主节点：UPDATE、DELETE、INSERT；并根据读写类型备策略为随机任选一个备。
                const pretix = tryGetSqlPretix(ele);
                const instances = this.getInstancesByRoute(pretix, 'service');
                routes.push({
                    sql: ele,
                    instanceIds: instances.map(o => o.value)
                });
            });
            try {
                const res = await getMemorySqlRouter({
                    productInstNo: localStorage.getItem('productInstNo'),
                    routes
                });
                if (res.success) {
                    Array.isArray(res.data) && res.data.forEach(ele => {
                        const instanceList = [];
                        ele.instanceIds.forEach(item => {
                            const data = _.find(this.instance, ['value', item]);
                            instanceList.push(data);
                        });
                        const node = {
                            sql: ele.sql,
                            instanceList
                        };
                        sqlData.push(node);
                    });
                }
            } catch (error) {
                console.error(error);
            } finally {
                this.resultList = [];
                this.$refs['sqlResult'].clearData();
                this.handleSqlRun(sqlList, sqlData);
            }
        },
        // SQL 判断
        hasSQLKeywords(sql) {
            // 转换为小写进行匹配
            const lowerCaseSQL = sql.toLowerCase();

            // 简单正则：匹配UPDATE、INSERT、DELETE（忽略字符串字面量中的情况）
            const regex = /\b(update|insert|delete)\b/gi;

            // 初步匹配
            const matches = lowerCaseSQL.match(regex);

            if (!matches) return false;

            // 简单过滤字符串字面量中的匹配（可能有局限性）
            let cleanSQL = lowerCaseSQL;
            // 移除单引号字符串
            cleanSQL = cleanSQL.replace(/'[^']*'/g, '');
            // 移除双引号字符串（MySQL支持）
            cleanSQL = cleanSQL.replace(/"[^"]*"/g, '');

            // 再次检查是否存在关键字
            return regex.test(cleanSQL);
        },

        // SQL执行
        // eslint-disable-next-line complexity
        async handleSqlRun(catchList, performConfirm) {
            const sqlList = catchList || this.$refs.sqlEditor.getSqlList();
            // 当前选择为集群还是节点
            const type = this.$refs['manage-top']?.$_getType() || '';
            if (!sqlList.length) {
                this.$hMessage.error('SQL输入不能为空！');
                return;
            } else if (!this.timeout) {
                this.$hMessage.error('超时时间不能配置为空或0！');
                return;
            }
            if (this.timeout > this.maxTimeWait) {
                this.$hMessage.error('超时时间不得超过10分钟！');
                return;
            }
            if (!performConfirm) {
                // 如果确认校验无初始值，则默认为true
                if (!localStorage.getItem('apm.mdbsql.performConfirm')) {
                    localStorage.setItem('apm.mdbsql.performConfirm', true);
                }
                // 弹窗确认
                if (localStorage.getItem('apm.mdbsql.performConfirm') === 'true' &&
                    type === 'service' && this.hasSQLKeywords(sqlList.join())
                ) {
                    this.performConfirmInfo.status = true;
                    this.performConfirmInfo.sqlList = sqlList;
                    this.performConfirmInfo.instances = this.instance;
                    return;
                }
                // 如果服务模式下不弹窗， 则直接执行
                if (type === 'service') {
                    this.getMemorySqlRouter(sqlList);
                    this.loadRuning = false;
                    return;
                }
            }

            this.loadRuning = true;
            const param = {
                productInstNo: localStorage.getItem('productInstNo'),
                endpointId: this.endpointId,
                page: 1,
                pageSize: 10,
                performSqlTimeout: this.timeout * 1000
            };
            if (!catchList) {
                this.resultList = [];
                this.$refs['sqlResult'].clearData();
            }
            let index = this.resultList.length;

            const product = this.$refs['manage-top']?.$_getProductInfo() || {};
            const sqls = [];
            while (sqlList.length) {
                const item = sqlList.splice(0, 1)[0];
                param.sql = this.addLimitToSQL(item);

                // SQL执行，APM 自动识别用户SQL是读SQL：SELECT，还是写操作集群中主节点：UPDATE、DELETE、INSERT；并根据读写类型备策略为随机任选一个备。
                const pretix = tryGetSqlPretix(param.sql);
                const instances = performConfirm
                    ? _.find(performConfirm, ['sql', param.sql])?.instanceList || []
                    : this.getInstancesByRoute(pretix, type);
                const time = new Date();

                if (!instances.length) {
                    this.$hMessage.warning('当前模式下无可执行的核心节点，请调整路由策略重试');
                    this.loadRuning = false;
                    return;
                }

                // 设置SQL执行参数
                this.setSqlExecutionParams(param, type, instances);
                // 创建SQL历史记录
                if (type === 'service') {
                    instances.forEach(ele => {
                        sqls.push(this.createSqlHistoryItem({ time, sql: param.sql, type, instance: ele, product }));
                    });
                } else {
                    sqls.push(this.createSqlHistoryItem({ time, sql: param.sql, type, instance: instances[0], product }));
                }

                const startTime = Date.now();
                try {
                    const res = await getMemoryDataBySql(param, param.performSqlTimeout + 2000); // 比后端时间多加1s，用来获取返回结果
                    let tableRecords, ast = {};
                    if (res.code === MDB_NO_LOGIN) {
                        this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
                        break;
                    }
                    this.$refs?.['manage-top'] && this.$refs['manage-top'].updateToken(res?.headers?.authorization);
                    if (res.success) {
                        tableRecords = res.data.tableRecords;
                        try {
                            ast = tryGetSqlSelectAst(param.sql);
                        } catch (error) {
                            console.error(error);
                        }
                    } else {
                        tableRecords = [{
                            error_info: res.message
                        }];
                    }
                    const totalTime = Date.now() - startTime;
                    const sqlRunTime = res.tracing && res.tracing.find(item => item.spanName === 'appApmSqlExecuteExitLinkLatency')?.duration;
                    const apmTime = res.tracing && res.tracing.find(item => item.spanName === 'appApmSqlExecuteEnterLinkLatency')?.duration;
                    const node = {
                        key: index,
                        label: '结果' + (index + 1),
                        columns: [],
                        sql: param.sql,
                        ast,
                        isExport: (ast.type === 'select' && ast?.from?.length === 1),
                        time: res?.data?.time || '-',
                        sqlRunTime: sqlRunTime || '-',
                        apmTime: apmTime || '-',
                        totalTime,
                        data: tableRecords,
                        pageSize: 10,
                        page: 1,
                        totalCount: res?.data?.totalCount,
                        instanceCount: res?.data?.instanceCount,
                        type,
                        id: type === 'service' ? instances?.[0]?.serviceKey : instances?.[0]?.value,
                        instanceName: type === 'service' ? instances?.[0]?.serviceName : instances?.[0]?.label,
                        param: { ...param },
                        instanceErrorMsg: res?.data?.instanceErrorMsg || [],
                        isAllError: (res?.data?.instanceErrorMsg || [])?.length === instances.length
                    };
                    if (Array.isArray(Object.keys(tableRecords?.[0] || {}))) {
                        Object.keys(tableRecords?.[0] || {}).forEach(ele => {
                            let width = getSpanWidth(ele);
                            width += 40;
                            node.columns.push({
                                title: ele === 'apm_node' ? 'node' : ele,
                                key: ele,
                                minWidth: width > 150 ? width : 150,
                                ellipsis: true,
                                fixed: ele === 'apm_node' ? 'left' : '',
                                renderHeader: ele === 'apm_node' ? (h, params) =>
                                    h('div', {}, [
                                        h(
                                            'span',
                                            {
                                                style: {
                                                    color: '#fff',
                                                    verticalAlign: '2px',
                                                    whiteSpace: 'pre-wrap'
                                                }
                                            },
                                            'node'
                                        ),
                                        h(
                                            'h-poptip',
                                            {
                                                class: 'apm-poptip',
                                                props: {
                                                    customTransferClassName: 'apm-poptip monitor-poptip',
                                                    transfer: true,
                                                    trigger: 'hover',
                                                    placement: 'top-start'
                                                }
                                            },
                                            [
                                                h('icon', {
                                                    props: { name: 'prompt1', color: '#9296A1', size: '14' },
                                                    style: { padding: '0 10px' }
                                                }),
                                                h(
                                                    'div',
                                                    {
                                                        slot: 'content',
                                                        class: 'pop-content'
                                                    },
                                                    [h('p', '仅表示行数据对应的SQL执行的节点，与执行结果无关')]
                                                )
                                            ]
                                        )
                                    ]) : ''

                            });
                        });
                        this.resultList.push(node);
                    }
                    if (!res.success) {
                        if (!sqlList.length) {
                            this.$hMessage.error(`${param.sql}' 执行异常!`);
                        } else {
                            this.$hMsgBoxSafe.confirm({
                                title: `SQL执行异常`,
                                okText: '继续',
                                cancelText: '终止',
                                content: `'SQL：${param.sql} 执行异常，您确定要继续执行剩余SQL吗？`,
                                onOk: () => {
                                    this.handleSqlRun(sqlList);
                                },
                                onCancel: () => {
                                    return;
                                }
                            });
                        }
                        break;
                    }
                } catch (error) {
                    console.error(error);
                    break;
                }
                index++;
            }
            this.insertSqlHistoryNew(sqls);
            this.loadRuning = false;
        },
        // 处理分页
        // eslint-disable-next-line max-params
        async handlePageChange(index, pageNo, pageSize, onSetPage) {
            if (this.isFetching) return;
            this.isFetching = true;
            const curTable = this.resultList[index];
            try {
                const startTime = Date.now();
                const param = {
                    productInstNo: localStorage.getItem('productInstNo'),
                    endpointId: this.endpointId,
                    // instanceId: curTable.id,
                    page: pageNo,
                    pageSize: pageSize,
                    sql: curTable.sql,
                    performSqlTimeout: this.timeout * 1000
                };
                // SQL执行，APM 自动识别用户SQL是读SQL：SELECT，还是写操作集群中主节点：UPDATE、DELETE、INSERT；并根据读写类型备策略为随机任选一个备。
                const type = this.$refs['manage-top']?.$_getType() || '';
                if (type === 'service') {
                    const instances = this.getInstancesByRoute('SELECT', type);
                    param.instanceIds = instances.map(item => item.value).join();
                    param.serviceCode = this.serviceCode;
                    param.instanceCount = curTable.instanceCount;
                } else {
                    param.instanceId = curTable.id;
                }
                let tableRecords = [];
                const res = await getMemoryDataBySql(param, param.performSqlTimeout + 2000); // 比后端时间多加1s，用来获取返回结果
                if (res.code === MDB_NO_LOGIN){
                    this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
                    return;
                }
                if (res.success) {
                    tableRecords = res.data?.tableRecords || [];
                    if (tableRecords?.length === 0) {
                        this.$hMessage.error('该页数据不存在，请重新执行');
                    }
                } else {
                    tableRecords = [{
                        error_info: res.message
                    }];
                }
                const sqlRunTime = res.tracing && res.tracing.find(item => item.spanName === 'appApmSqlExecuteExitLinkLatency')?.duration;
                const apmTime = res.tracing && res.tracing.find(item => item.spanName === 'appApmSqlExecuteEnterLinkLatency')?.duration;
                const totalTime = Date.now() - startTime;
                this.resultList[index].data = tableRecords;
                this.resultList[index].pageSize = param.pageSize;
                this.resultList[index].page = param.page;
                this.resultList[index].time = res?.data?.time || '-';
                this.resultList[index].sqlRunTime = sqlRunTime || '-';
                this.resultList[index].apmTime = apmTime || '-';
                this.resultList[index].totalTime = totalTime;
                onSetPage(param.page, param.pageSize);
                return tableRecords;
            } finally {
                this.isFetching = false;
            }
        },
        // 打开sql文件执行弹窗
        showSqlFileModal()  {
            if (!this.timeout) {
                this.$hMessage.error('超时时间不能配置为空！');
                return;
            }
            if (this.timeout > this.maxTimeWait) {
                this.$hMessage.error('超时时间不得超过10分钟！');
                return;
            }
            this.sqlInfo.status = true;
            this.sqlInfo.endpointId = this.endpointId;
            this.sqlInfo.instance = this.instance;
            this.sqlInfo.timeout = this.timeout;
            this.sqlInfo.serviceCode = this.serviceCode;
            this.sqlInfo.type = this.$refs['manage-top'].$_getType();
        },
        async insertSqlHistoryNew(sqls) {
            const history = localStorage.getItem('HISTORY_SQLS_NEW');
            let list = [];
            if (history) {
                list = JSON.parse(history);
                list.length >= 100 && list.pop();
            }
            list.unshift(sqls);
            localStorage.setItem('HISTORY_SQLS_NEW', JSON.stringify(list));
        },
        // 将历史sql插入编辑器
        addSqlToEdit(sqls) {
            sqls.forEach(ele => {
                this.$refs['sqlEditor'].addSqlToEdit(ele);
            });
        },
        // 点击左侧表名直接查询当前数据
        handleTableClick(arr, node) {
            if (this.loadRuning) {
                this.$hMessage.info('有正在执行的sql，请稍后再试！');
                return;
            }
            if (node.originTableName) {
                this.resultList = [];
                this.$refs['sqlResult'].clearData();
                this.handleSqlRun([`select * from ${node.originTableName};`]);
            }
        },
        // 现实错误日志
        showErrorInfoModal(arr) {
            this.sqlErrorInfo.status = true;
            this.sqlErrorInfo.instanceErrorMsg = [...arr];
        },
        changeShowHidden() {
            this.sqlEditVisible = !this.sqlEditVisible;
            this.$nextTick(() => {
                this.resize();
            });
        },
        // 编辑框上下滑动
        mouseDown(e) {
            if (!this.sqlEditVisible) return;
            this.startY = e.clientY;
            this.mouseMove(e);
            this.mouseUp();
            document.addEventListener('mousemove', this.mouseMove);
            document.addEventListener('mouseup', this.mouseUp);
        },
        mouseUp() {
            this.editHeight = this.lastEditHeight;
            document.removeEventListener('mousemove', this.mouseMove);
            document.removeEventListener('mouseup', this.mouseUp);
        },
        mouseMove(e) {
            e.preventDefault();
            e.stopPropagation();
            if (e.clientY < 220 || e.clientY > 356) return;
            const offset = e.clientY - this.startY;
            if (offset) {
                this.lastEditHeight = offset + this.editHeight;
            }
        }
    },
    watch: {
        searchText(val) {
            this.treeFilterMethod(val);
        },
        isOnlyRedo(a) {
            const val = this.searchText;
            this.treeFilterMethod(val);
            this.schemaList = a ? _.filter(this.tableList, ['hasWriteRedo', false]) : this.tableList;
            this.$nextTick(() => {
                this.$refs['menu2'].remakeMenuList(this.schemaList);
            });
        },
        isShowTable() {
            this.$nextTick(() => {
                this.resize();
            });
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");

.title-line {
    position: relative;
    width: 100%;
}

.product-box {
    min-width: 900px;

    /deep/ .h-tabs-content-wrap {
        height: calc(100% - 48px);
    }

    /deep/.h-tabs-nav-right {
        position: fixed;
        width: 100%;
    }

    /deep/ .h-tabs-bar {
        margin-bottom: 0;
    }

    /deep/ .h-tabs-return,
    /deep/ .h-tabs-enter {
        padding: 4px 5px 0 0;
    }

    /deep/ .apm-box {
        margin-top: 0;
        height: 100%;
    }

    /deep/ .h-input-group {
        border: 1px solid var(--base-color);
        border-radius: 4px;
    }

    /deep/ .h-input-group > .h-input-group-prepend {
        background-color: var(--button-bg-color);
        color: #fff;
        border: none;
    }

    /deep/ .h-input-group > .h-input-group-append {
        background-color: var(--button-bg-color);
        color: #fff;
        border: none;
        border-left: 1px solid var(--base-color);
    }

    /deep/ .h-input-group > .h-input {
        border: none;
        background-color: var(--button-bg-color);
    }

    .input-line {
        display: inline-block;
        width: 1px;
        height: 34px;
        margin: 0 4px;
        text-align: center;
        background: var(--base-color);
    }

    /deep/ .h-table-cell span {
        white-space: pre;
    }
}

.menu-box {
    height: 100%;
    overflow: hidden;

    // 菜单搜索栏
    .search-bar {
        height: 44px;
        padding: 5px 5px 10px;
        border-bottom: var(--border);
    }

    /deep/ .h-tree > li {
        min-height: 40px;
    }

    // 菜单树组件
    .menu-tree {
        height: calc(100% - 85px);
        overflow: auto;
        padding: 8px 4px;
        border-bottom: 1px solid var(--border-color);

        /deep/ .h-tree-title {
            background: none;
            color: #fff;

            &:hover {
                background: none;
                color: var(--link-color);
            }
        }

        /deep/ .h-tree-arrow {
            color: #fff;

            & > .h-icon-ios-arrow-right::before {
                content: "\25B8";
                font-size: 17px;
            }
        }

        /deep/ .h-tree-item {
            position: relative;

            & > .h-tree-arrow {
                line-height: 40px;
            }

            &:hover {
                background-color: #1f3759;
            }
        }

        /deep/ .h-tree-children {
            & > li > .h-tree-item > .h-tree-title {
                color: #bcbcbc;

                &:hover {
                    background: none;
                }
            }
        }
    }

    // tab栏
    /deep/ .h-tabs-nav-wrap {
        width: 100%;
    }

    /deep/ .h-tabs-nav-right {
        float: right;
    }
}

.sql-resizer {
    width: 100%;

    .y-resizer {
        position: absolute;
        left: 0;
        right: 0;
        top: -2px;
        height: 1px;
        user-select: none;
        cursor: row-resize;
        background-color: var(--base-color);

        &:hover {
            height: 3px;
            background-color: var(--base-color);
        }
    }

    .cate-switch {
        position: absolute;
        width: 44px;
        height: 12px;
        left: 50%;
        top: 0;
        line-height: 12px;
        text-align: center;
        background: rgba(209, 216, 229, 0.2);
        border-radius: 0;
        z-index: 4;
        cursor: pointer;
        transform: perspective(0.5em) rotateX(-10deg);

        &:hover {
            background: rgba(209, 216, 229, 0.4);
        }
    }
}

</style>
