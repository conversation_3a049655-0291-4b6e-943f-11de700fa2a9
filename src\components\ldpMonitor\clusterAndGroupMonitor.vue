<template>
    <div class="cluster-and-group-monitor">
        <waterfall-layout v-if="!!services.length" :id="id" :ref="id" :key="id" :history="true" :items="services"
            itemKey="id" :gap="[9, 20]" :cols="{ 1014: 3, 1174: 4, 1654: 5 }" :isEqualHeight="true">
            <template #item="{ item }">
                <business-box-panel :id="`${id}-${item['id']}`" :key="`${id}-${item['id']}`" :panel="item" :type="type"
                    :mode="groupMode" />
            </template>
        </waterfall-layout>
        <no-data v-else />
        <a-loading v-if="loading" style="width: 100%; height: 100%;"></a-loading>
    </div>
</template>
<script>
import WaterfallLayout from '../common/draggableWaterfall/waterfallLayout.vue';
import noData from '@/components/common/noData/noData';
import { getObjByArray } from '@/utils/utils';
import { getClusterRoles, getInstancesObservablesNode, getMonitorHeartbeats, getClustersObservablesNode } from '@/api/httpApi';
import { getProductObservation } from '@/api/topoApi';
import aLoading from '@/components/common/loading/aLoading';
import BusinessBoxPanel from '../ldpProduct/businessBox/businessBoxPanel.vue';
import _ from 'lodash';
const roomTypeEnum = {
    1: '主',
    2: '同城',
    3: '异地',
    4: '未知'
};
export default {
    components: { WaterfallLayout, BusinessBoxPanel, aLoading, noData },
    props: {
        productInstNo: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        },
        hiddenArbInstance: {
            type: Boolean,
            default: undefined
        },
        groupMode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            services: [],
            timer: undefined,
            loading: false
        };
    },
    computed: {
        id() {
            return `cluster-and-group-monitor-${this.productInstNo}-${this.type}-${this.mode}`;
        }
    },
    beforeDestroy() {
        this.clearPolling();
    },
    methods: {
        // 初始化
        async init() {
            try {
                this.clear();
                if (this.groupMode !== 'service' && this.groupMode !== 'deploy') {
                    return;
                }
                this.loading = true;
                this.services = await this.getServices();
                if (this.services.length) {
                    this.setPolling();
                }
            } catch (e) {
                console.error(e);
                this.clear();
            } finally {
                this.loading = false;
            }
        },

        clear() {
            this.services = [];
            this.clearPolling();
        },

        // 初始化
        async getServices() {
            const dashboards = await this.getObservablesDashboards();
            const nodes = await this.getObservablesNodes();

            if (!nodes || !dashboards) {
                return [];
            }

            return this.composeServiceData({ dashboards, nodes });
        },

        // 获取模版接口
        async getObservablesDashboards() {
            // 应用节点观测仪表盘
            const response = await getProductObservation({
                productId: this.productInstNo,
                observationMode: this.groupMode,
                hiddenArbInstance: this.hiddenArbInstance
            });

            if (!response.success) {
                return;
            }

            return response?.data?.services || response?.data?.rooms;
        },

        // 应用集群信息列表查询
        async getObservablesNodes() {
            if (this.type !== 'instance' && this.type !== 'cluster') {
                return;
            }

            const requestParamete = { productId: this.productInstNo };
            const response = this.type === 'instance'
                ? await getInstancesObservablesNode(requestParamete)
                : await getClustersObservablesNode(requestParamete);

            if (!response.success || !Array.isArray(response.data)) {
                return [];
            }

            return getObjByArray(response.data, 'id') || [];
        },

        // 转换 service 数据
        composeServiceData({ dashboards = [], nodes = [] }) {
            const services = [];
            for (const dashboard of dashboards) {
                const item = this.groupMode === 'service'
                    ? this.composeSerivceGroupModeDashBoard(dashboard, nodes)
                    : this.composeDeployGroupoModeDashBoard(dashboard, nodes);

                services.push(item);
            }
            return this.updateStatus(services);
        },

        // 转换 groupMode 为 service 的数据结构
        composeSerivceGroupModeDashBoard(dashboard, nodes) {
            const appClusters = dashboard?.appClusters || [];
            const content = appClusters.map(cluster => ({
                id: cluster.id,
                label: cluster.clusterName,
                nodes: this.composeNode(cluster?.instances, nodes),
                target: {
                    resourceId: cluster.id,
                    resourceNameLeft: cluster.shardingNo ? `分片${cluster.shardingNo}` : '',
                    resourceNameRight: `${this.$store.state?.apmDirDesc?.clusterTypeDict?.[cluster?.clusterType?.toUpperCase()]}: ${cluster?.memberNumber}`
                }
            }));

            return {
                id: dashboard.serviceCode,
                name: dashboard.serviceName,
                content
            };
        },

        // 转换 groupMode 为 depoly 的数据结构
        composeDeployGroupoModeDashBoard(dashboard, nodes) {
            const hosts = dashboard?.hosts || [];
            const content = hosts.map(host => ({
                id: host.id,
                label: host.hostNameAlias || '-',
                nodes: this.composeNode(host?.instances, nodes),
                target: {
                    resourceId: host.id,
                    resourceNameLeft: host.hostNameAlias ? host.hostNameAlias : '',
                    resourceNameRight: `${host.ips?.[0] || '-'}`
                }
            }));

            return {
                id: dashboard.roomId,
                name: dashboard.roomNameAlias || dashboard.roomName,
                sign: roomTypeEnum?.[dashboard.roomType],
                content
            };
        },

        // 转换 node 数据结构
        composeNode(data, nodes) {
            if (!Array.isArray(data)) {
                return [];
            }

            return data.map(instance => ({
                id: instance.id,
                label: instance.instanceName,
                target: {
                    resourceId: instance.id,
                    resourceName: instance.instanceName,
                    resourceType: instance.instanceType,
                    instanceInfo: instance,
                    baseInfo: { ...nodes[instance.id] },
                    runningInfo: { ...nodes[instance.id]?.runningInfo },
                    operateInfo: { ...nodes[instance.id]?.operateInfo },
                    observations: nodes[instance.id]?.observations || []
                }
            }));
        },

        // 更新节点状态、集群信息、分组信息
        async updateStatus(services) {
            const appStatus = await this.getMonitorHeartbeats();
            const clusterRoles = await this.getClusterRoles();

            for (const { content } of services) {
                this.updateSingleServiceStatus(content, appStatus, clusterRoles);
            }
            return services;
        },

        // 获取心跳数据
        async getMonitorHeartbeats() {
            const response = await getMonitorHeartbeats({
                productId: this.productInstNo,
                type: this.type
            });

            if (!response.success || !Array.isArray(response.data)) {
                return {};
            }

            return getObjByArray(response.data, 'id');
        },

        // 获取集群角色
        async getClusterRoles() {
            const response = await getClusterRoles({
                productId: this.productInstNo
            });

            if (!response.success || !Array.isArray(response.data)) {
                return {};
            }

            return getObjByArray(response.data, 'instanceId');
        },

        updateSingleServiceStatus(content, appStatus, clusterRoles) {
            for (const { nodes } of content) {
                for (const node of nodes) {
                    node.target.runningInfo = {
                        ...(node.target.runningInfo || {}),
                        ...(appStatus[node?.target.resourceId] || {}),
                        clusterRole: clusterRoles[node?.target?.resourceId]?.clusterRole || ''
                    };

                    // 集群成员--集群角色
                    if (this.type === 'cluster' && node?.target?.baseInfo?.members?.length) {
                        for (const member of Object.values(node?.target?.baseInfo?.members || [])) {
                            member.clusterRole = clusterRoles[member.id]?.clusterRole || '-';
                        }
                    }
                }
            }
        },

        // 定时器
        setPolling() {
            this.clearPolling();
            this.timer = setTimeout(async () => {
                try {
                    const service = await this.updateStatus(this.services);
                    this.services = _.cloneDeep(service);
                    this.setPolling();
                } catch (e) {
                    console.error(e);
                }
            }, 10000);
        },

        clearPolling() {
            if (this.timer !== undefined) {
                clearTimeout(this.timer);
                this.timer = undefined;
            }
        },

        editLayout() {
            this.clearPolling();
            this.$refs[this.id].edit();
        },

        confirmEditLayout() {
            this.$refs[this.id].okEdit();
            this.setPolling();
        },

        cancelEditLayout() {
            this.$refs[this.id].cancelEdit();
            this.setPolling();
        },

        revertLayout() {
            this.$refs[this.id].revertLayout();
        }
    }
};
</script>
<style lang="less" scoped>
.cluster-and-group-monitor {
    height: 100%;
}
</style>
