<template>
    <h-dropdown class="button-dropdown" trigger="click" @on-click="handleSelect">
        <h-button class="dropdwon-button" type="primary">
            {{ title }}
            <h-icon name="unfold"></h-icon>
        </h-button>
        <h-dropdown-menu slot="list">
            <h-dropdown-item v-for="({ value, label }) of items" :key="value" :name="value">
                {{ label }}
            </h-dropdown-item>
        </h-dropdown-menu>
    </h-dropdown>
</template>

<script>
export default {
    name: 'ButtonDropdown',
    props: {
        title: {
            type: String,
            required: true
        },
        items: {
            type: Array,
            required: true
        }
    },
    methods: {
        handleSelect(value) {
            this.$emit('select', value);
        }
    }
};
</script>

<style lang="less" scoped>
.button-dropdown {
    height: 42px;

    /deep/ .h-dropdown-rel {
        position: relative;
        top: -2px;
        color: #495060;
        font-size: 14px;
    }

    /deep/ button.dropdwon-button {
        padding: 6px 6px 6px 12px;
        color: var(--font-color);
        background-color: var(--input-bg-color);
        border: var(--border);

        .h-icon {
            margin-left: 9px;
        }
    }

    /deep/ .h-select-dropdown {
        margin-top: 3px;
    }

    /deep/ .h-dropdown-menu {
        padding: 8px 0;
        background: #262d43;
        border: 1px solid #485565;
        border-radius: 4px;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);

        .h-dropdown-item {
            color: #fff;

            &:hover {
                background: #1f3759;
            }
        }
    }
}
</style>
