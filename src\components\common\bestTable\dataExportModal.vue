<template>
    <div>
        <!-- 数据导出 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            :title="$t('pages.common.exportDataLabel')"
            width="700"
            height="480"
            @on-open="handleModalOpen"
            @on-close="handleModalClose"
        >
            <div class="export-title">{{ $t('pages.common.exportFieldConfig') }}</div>
            <div>
                <h-form :label-width="200" style="margin-top: 10px;">
                    <h-form-item
                        :label="$t('pages.common.queryFieldName')" style="font-weight: 600; margin-bottom: 10px;">
                        <div>{{ $t('pages.common.exportFieldName') }}</div>
                    </h-form-item>
                </h-form>
                <h-form
                    ref="formValidate"
                    class="export-form-box"
                    :model="formValidate"
                    :label-width="200"
                    >
                   <h-form-item
                        v-for="item in configList"
                        :key="item.fieldId"
                        :label="item.fieldName"
                        style="margin-bottom: 10px;">
                        <h-input
                            v-model="formValidate[item.fieldId]"
                            :placeholder="$t('pages.common.inputAliasPrompt')"
                            :disabled="exportStatus === 'export'"
                            style="width: 200px;"
                        ></h-input>
                    </h-form-item>
                </h-form>
            </div>
            <div class="export-title" style="margin-top: 15px;">{{ $t('pages.common.exportExecutionStatus') }}</div>
            <h-form :label-width="120" style="margin-top: 10px;">
                <h-form-item :label="$t('pages.common.exportFileLabel') + ':'" style="margin-bottom: 5px;">
                    <div>{{ exportFileName }}</div>
                </h-form-item>
                <h-form-item :label="$t('pages.common.exportProgress') + ':'" style="margin-bottom: 5px;">
                    <div>{{ exportProgress }}
                        <a-button
                            v-if="exportStatus === 'end'"
                            class="btn-download"
                            type="text"
                            style="color: #0063d4;"
                            :loading="loading"
                            @click="downloadExportFile">
                        {{ $t('pages.common.downloadLabel') }}</a-button>
                    </div>
                </h-form-item>
                <h-form-item :label="$t('pages.common.executionInfo') + ':'" style="margin-bottom: 5px;">
                    <div>{{ executionInfo }}</div>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button type="primary" :loading="exportStatus === 'export'" @click="submitConfig">{{exportStatus === 'export' ? $t('pages.common.exporting') : $t('pages.common.startExport')}}</a-button>
                <a-button :loading="stopLoading" :disabled="exportStatus !== 'export'" @click="discontinueExportFileData">{{ stopLoading ? $t('pages.common.terminatingLabel') : $t('pages.common.terminateExport')}}</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { getFileExportConfig, getFileExportStatus, exportFileData, discontinueExportFileData, downloadExportFile } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            stopLoading: false,
            formValidate: {},
            configList: [],
            timer: null,
            exportFileName: '-',
            exportProgress: '-',
            executionInfo: '-',
            filePath: '',
            exportStatus: ''
        };
    },
    methods: {
        handleModalOpen() {
            this.handleModalClose();
            this.getFileExportConfig();
            this.getFileExportStatus();
            this.timer && clearInterval(this.timer);
            this.timer = setInterval(() => {
                this.getFileExportStatus();
            }, 3000);
        },
        // 获取当前是否有导出任务
        async getFileExportStatus() {
            const param = {
                productInstNo: localStorage.getItem('productInstNo'),
                exportScene: this.modalInfo.exportScene
            };
            try {
                const res = await getFileExportStatus(param);
                const data = res?.data || {};
                this.exportFileName = data.exportFileName || '-';
                this.exportProgress = data.exportProgress || '-';
                this.executionInfo = data.executionInfo || '-';
                this.filePath = data.exportFilePath || '';
                this.exportStatus = data.exportStatus || '';
            } catch (error) {
                this.timer && clearInterval(this.timer);
                console.log(error);
            }
        },
        // 获取导出接口表头配置列表
        async getFileExportConfig() {
            const param = {
                productInstNo: localStorage.getItem('productInstNo'),
                bizType: this.modalInfo.queryBody.bizType,
                bizTraceType: this.modalInfo.queryBody.bizTraceType,
                exportScene: this.modalInfo.exportScene
            };
            let data = [];
            try {
                const res = await getFileExportConfig(param);
                data = res.data || [];
            } catch (error) {
                console.log(error);
            }
            this.configList = data;
            this.configList.forEach(ele => {
                this.$set(this.formValidate, ele.fieldId, ele.fieldAlias);
            });
        },
        // 开始导出
        async submitConfig() {
            try {
                const res = await exportFileData({
                    productInstNo: localStorage.getItem('productInstNo'),
                    exportScene: this.modalInfo.exportScene,
                    queryBody: this.modalInfo.queryBody,
                    exportFiledList: Object.entries(this.formValidate).map(
                        ([key, value]) => ({ fieldId: key, fieldAlias: value })
                    )
                });
                if (res.success) {
                    this.getFileExportStatus();
                } else {
                    this.$hMessage.error(res?.data?.message || this.$t('pages.common.exportFailedMessage'));
                }
            } catch (err) {
                console.log(err);
            }
        },
        // 终止导出
        async discontinueExportFileData() {
            this.stopLoading = true;
            const param = {
                productInstNo: localStorage.getItem('productInstNo'),
                exportScene: this.modalInfo.exportScene
            };
            try {
                const res = await discontinueExportFileData(param);
                if (res.success) {
                    this.$hMessage.success(this.$t('pages.common.operationSuccess'));
                    this.getFileExportStatus();
                } else {
                    this.$hMessage.error(res.message || this.$t('pages.common.operationFailed'));
                }
            } catch (error) {
                console.log(error);
            }
            this.stopLoading = false;
        },
        // 下载文件
        async downloadExportFile() {
            this.loading = true;
            const res = await downloadExportFile({
                filePath: this.filePath
            });
            const blob = new Blob([res]);
            const objectUrl = URL.createObjectURL(blob);
            // 创建a标签链接并点击
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = objectUrl;
            const subFilePath = this.filePath.split('/');
            link.download = `${subFilePath.at(-1)}`;
            document.body.appendChild(link);
            link.click();
            // 创建a标签链接并点击 end
            document.body.removeChild(link);
            window.URL.revokeObjectURL(blob);
            this.loading = false;
        },
        // 关闭时，销毁定时器
        handleModalClose() {
            this.timer && clearInterval(this.timer);
        }
    },
    components: { aButton }
};
</script>
<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px;
        user-select: text;
    }

    .export-title {
        font-weight: 600;
        border-bottom: 1px solid #ccc;
        padding-bottom: 6px;
    }

    .export-form-box {
        height: 200px;
        overflow: auto;
    }

    .btn-download {
        color: #0063d4;

        &:hover {
            text-decoration: underline;
        }
    }
</style>
