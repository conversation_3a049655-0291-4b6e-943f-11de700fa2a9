.best-table {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 0;

    .form-box {
        overflow-y: hidden;
        transition: height 0.5s;
        border-radius: 4px;
    }

    .h-page {
        float: right;
        height: 42px;
    }

    // 含有tab页的表格页面
    .h-tabs {
        height: 100%;
    }

    .h-tabs .h-tabs-content-wrap {
        height: 100%;
        overflow: hidden;
    }

    .h-tabs .h-tabs-tabpane {
        height: 100%;
        overflow: hidden;
    }

    .h-tabs .h-tabs-content {
        height: 100%;
    }

    .h-tabs-nav .h-tabs-tab {
        color: var(--font-color);
    }

    .h-tabs-nav .h-tabs-tab-active {
        color: var(--link-color);
    }

    .h-tabs-bar {
        margin-bottom: 0;
        border-color: var(--border-color);
    }

    .table-box {
        width: 100%;
        height: 100%;
        margin-top: 10px;
        background-color: #262b40;
        border-radius: 4px;

        .table-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 48px;
            border-bottom: 1px solid #31364a;

            & > .table-title-text {
                padding-left: 33px;
                font-size: 14px;
                color: #fff;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap;

                .additional-title-text {
                    color: #a5a5a5;
                }

                &::before {
                    display: inline-block;
                    position: relative;
                    left: -15px;
                    top: 3px;
                    content: "";
                    width: 5px;
                    height: 17px;
                    background: var(--link-color);
                }
            }
        }

        & > .a-table {
            padding: 0 15px;
        }
    }
}
