<template>
    <!-- 主体内容 -->
    <h-tabs
        v-model="tabName"
        class="product-box"
        @on-click="tabClick(tabName)">
        <h-tab-pane
            v-for="item in editableTabs"
            :key="item.name"
            :label="$t(item.describe)"
            :name="item.name">
            <component
                :is="item.component"
                v-if="tabName === item.name"
                :ref="item.name"
                :productId="productId"
                @page-jump="handlePageJump">
            </component>
        </h-tab-pane>
    </h-tabs>
</template>

<script>
import latestAppearanceInfo from '@/components/dataSecondAppearance/secondAppearanceDetail/latestAppearanceInfo.vue';
import appearanceHistory from '@/components/dataSecondAppearance/secondAppearanceDetail/appearanceHistory.vue';
export default {
    name: 'SecondAppearanceDetail',
    components: {
        latestAppearanceInfo,
        appearanceHistory
    },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tabName: 'latestAppearance',  // 初始标签页
            editableTabs: [
                {
                    name: 'latestAppearance',
                    describe: 'pages.dataSecondAppearance.latestAppearance',
                    component: 'latest-appearance-info'
                },
                {
                    name: 'historyAppearance',
                    describe: 'pages.dataSecondAppearance.appearanceHistory',
                    component: 'appearance-history'
                }
            ]
        };
    },
    methods: {
        // 初始化数据
        initData() {
            this.tabClick('latestAppearance');
        },
        // 标签页点击事件处理
        tabClick(name) {
            this.tabName = name;
            this.$nextTick(() => {
                this.$refs[name]?.[0] && this.$refs[name][0].initData();
            });
        },
        // 页面跳转事件处理
        handlePageJump(pageName) {
            this.$emit('page-jump', pageName);
        }
    }
};
</script>

<style lang="less" scoped>
.product-box {
    width: 100%;
    height: 100%;
    min-width: 1000px;

    /deep/ .h-tabs-bar {
        margin-bottom: 5px;
    }

    /deep/ .h-tabs-nav-wrap {
        float: none !important;
    }

    /deep/ .h-tabs-nav-right {
        position: absolute;
        right: 0;
        top: 5px;
    }
}
</style>
