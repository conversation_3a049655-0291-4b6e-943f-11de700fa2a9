/* eslint-disable no-unused-expressions */
/* eslint-disable max-params */
// eslint-disable-next-line max-params
// 用于管理功能 机房-类型-集群-节点汇总
import { transferVal } from '@/utils/utils';
import i18n from '@/locales/i18n';

export const setMenuList = (allTodoInstanceList, roomTableData, clusterRoles, appTypeDictDesc, subGroupSimpleClick = false) => {
    // 生成汇总数据
    const summary = {
        roomDefault: {
            menuId: 'roomDefault',
            menuName: i18n.t('utils.totalItems'),
            groupList: []
        }
    };
    const processedInstanceIds = new Set();

    // 建立从实例ID到实例详细信息的映射
    const instanceMap = allTodoInstanceList.reduce((map, instance) => {
        map[instance?.id || instance?.instanceId] = instance;
        return map;
    }, {});

    // 辅函数
    const getClusterId = (instance) => {
        return {
            clusterId: !transferVal(instance?.clusterId) ? 'default' : transferVal(instance?.clusterId),
            clusterName: !transferVal(instance?.clusterName) ? 'default' : transferVal(instance?.clusterName)
        };
    };

    const handleInstance = (roomId, roomName, instance, instanceId) => {
        const { clusterId, clusterName } = getClusterId(instance);
        const groupKey = `${roomId},${instance.instanceType}`;
        const groupName = appTypeDictDesc?.[instance.instanceType] || instance.instanceType;
        const subGroupKey = subGroupSimpleClick ? `${clusterId}` : `${roomId},${clusterId}`;
        const subGroupLabel = clusterName === 'default' ? i18n.t('utils.unknownCluster') : clusterName;

        if (!summary[roomId]) {
            summary[roomId] = {
                menuId: roomId,
                menuName: roomName,
                groupList: []
            };
        }

        const group = summary[roomId].groupList.find(group => group.key === groupKey) || {
            key: groupKey,
            label: groupName,
            subGroupList: []
        };

        if (!summary[roomId].groupList.includes(group)) {
            summary[roomId].groupList.push(group);
        }

        const subGroup = group.subGroupList.find(subGroup => subGroup.key === subGroupKey) || {
            key: subGroupKey,
            label: subGroupLabel,
            nodes: []
        };

        if (!group.subGroupList.includes(subGroup)) {
            group.subGroupList.push(subGroup);
        }

        subGroup.nodes.push({
            label: instance?.instanceName || instance?.instanceNo,
            value: instanceId,
            badge: clusterRoles[instanceId]?.clusterRole === 'ARB_ACTIVE'
        });
    };

    // 先处理所有实例并添加到 roomDefault
    allTodoInstanceList.forEach(instance => {
        const instanceId = instance?.id || instance?.instanceId;
        handleInstance('roomDefault', i18n.t('utils.totalItems'), instance, instanceId);
        processedInstanceIds.add(instanceId);
    });

    // 处理已分配机房，节点在allTodoInstanceList中的key
    roomTableData.forEach(room => {
        room.instanceIds.forEach(instanceId => {
            const instance = instanceMap[instanceId];
            if (instance) {
                handleInstance(room.roomId, room?.roomNameAlias || room?.roomName, instance, instanceId);
            }
        });
    });

    // 转换成数组形式，并进行排序
    const menuList = Object.values(summary);

    // 对 `roomId` 排序，将 'roomDefault' 放在最前面
    menuList.sort((a, b) => {
        if (a.menuId === 'roomDefault') return -1;
        if (b.menuId === 'roomDefault') return 1;
        return b.menuName.localeCompare(a.menuName);
    });

    // 排序函数
    const sortGroups = (groupList) => {
        groupList.sort((a, b) => (a.key?.split(',')[1] || '').localeCompare(b.key?.split(',')[1] || ''));

        groupList.forEach(group => {
            group.subGroupList?.sort((a, b) => {
                if (a.key.includes('default')) return -1;
                if (b.key.includes('default')) return 1;
                return a.label.localeCompare(b.label);
            });

            group.subGroupList?.forEach(subGroup => {
                subGroup.nodes?.sort((a, b) => {
                    if (a.badge) return -1;
                    if (b.badge) return 1;
                    return a.label.localeCompare(b.label);
                });
            });
        });
    };

    // 对 groupList、subGroupList和 nodes 进行排序
    menuList.forEach(room => {
        sortGroups(room.groupList);
    });

    return menuList;
};

