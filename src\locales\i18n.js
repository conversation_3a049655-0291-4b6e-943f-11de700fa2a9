// i18n.js
import Vue from 'vue';
import VueI18n from 'vue-i18n';
// HUI 组件库的语言包
import zhLocale from 'h_ui/dist/locale/zh-CN';
import enLocale from 'h_ui/dist/locale/en-US';
// 自定义语言包
import customLocales from '@/locales';

Vue.use(VueI18n);

// 合并语言包
const messages = {
    'zh-CN': {
        ...zhLocale,
        ...customLocales['zh-CN']
    },
    'en-US': {
        ...enLocale,
        ...customLocales['en-US']
    }
};

// 从本地存储获取语言设置，默认为中文
const savedLanguage = localStorage.getItem('apm-language') || 'zh-CN';

// 创建 VueI18n 实例
const i18n = new VueI18n({
    locale: savedLanguage, // 设置默认语言
    messages, // 设置语言包
    silentTranslationWarn: true // 去掉 warning 提示
});

export default i18n;
