<template>
    <div class="create-step">
        <h-steps
            :current="currentStep"
            class="steps-box">
            <h-step
                v-for="item in stepList"
                :key="item"
                :title="item">
            </h-step>
        </h-steps>

        <div ref="content-box" class="content-box">
            <a-loading v-if="loading"></a-loading>
            <!-- 提示信息 -->
            <div class="tips-box">
                <div class="tip-content">
                    {{ createTips[currentStep].content }}
                </div>
            </div>

            <!-- 确定上场内容 -->
            <div v-if="currentStep === 0" class="step-content">
                <table-selector
                    ref="tableSelecor"
                    :supportAppearTables="supportAppearTables"
                    :selectedAppearTables="selectedAppearTables"
                    @update-tables="handleUpdateTables">
                </table-selector>
            </div>

            <!-- 配置上场规则 -->
            <div v-if="currentStep === 1" class="step-content">
                <configure-rules
                    ref="configeRule"
                    :tableRulesList="selectedAppearTables"
                    @update-rules="handleUpdateRules">
                </configure-rules>
            </div>

            <!-- 信息核对 -->
            <div v-if="currentStep === 2" class="step-content">
                <a-simple-table
                    showTitle
                    :columns="columns"
                    :tableData="selectedAppearTables"
                    :hasPage="false"
                    :height="confirmTableHeight">
                </a-simple-table>
            </div>
        </div>

        <div class="buttom-box">
            <a-button v-show="currentStep" type="dark" @click="handleStepChange('-')">
                {{ $t('pages.dataSecondAppearance.previousStep') }}
            </a-button>
            <a-button
                v-show="currentStep !== 2"
                type="primary"
                :loading="nextStepLoading"
                @click="handleStepChange('+')">
                {{ $t('pages.dataSecondAppearance.nextStep') }}{{ stepList[currentStep + 1] }}
            </a-button>
            <a-button
                v-show="currentStep === 2"
                type="primary"
                :loading="confirmLoading"
                @click="handleAppearance">
                {{ $t('pages.dataSecondAppearance.appearance') }}
            </a-button>
            <a-button type="dark" @click="handleCancel">{{ $t('pages.dataSecondAppearance.cancel') }}</a-button>
        </div>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import aSimpleTable from '@/components/common/table/aSimpleTable';
import aLoading from '@/components/common/loading/aLoading';
import tableSelector from './tableSelector.vue';
import configureRules from './configureRules.vue';
import { executeAppearance } from '@/api/brokerApi';
import { getTablesMetaInfo } from '@/api/productApi';
import { LOADING_TYPE_OPTIONS, CREATE_TASK_TIPS } from '@/components/dataSecondAppearance/constant.js';
export default {
    name: 'CreateAppearanceTask',
    components: { aButton, aSimpleTable, aLoading, tableSelector, configureRules },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            currentStep: 0,
            confirmLoading: false,
            nextStepLoading: false,
            stepList: [this.$t('pages.dataSecondAppearance.confirmAppearanceContent'), this.$t('pages.dataSecondAppearance.configAppearanceRule'), this.$t('pages.dataSecondAppearance.infoCheck')],
            createTips: CREATE_TASK_TIPS,

            supportAppearTables: [], // 可上场表
            selectedAppearTables: [], // 选择的上场表

            columns: [
                {
                    title: this.$t('pages.dataSecondAppearance.tableName'),
                    key: 'tableName',
                    minWidth: 150,
                    ellipsis: true
                },
                {
                    title: this.$t('pages.dataSecondAppearance.cluster'),
                    key: 'clusterName',
                    minWidth: 120,
                    ellipsis: true
                },
                {
                    title: this.$t('pages.dataSecondAppearance.sharding'),
                    key: 'shardingNo',
                    width: 90,
                    ellipsis: true
                },
                {
                    title: this.$t('pages.dataSecondAppearance.loadMode'),
                    key: 'importMode',
                    minWidth: 120,
                    ellipsis: true,
                    render: (_, params) => {
                        const importMode = LOADING_TYPE_OPTIONS.find(o => o.value === params.row.importMode)?.label || '';
                        return <div class="h-table-cell-ellipsis" title={importMode}>
                            {importMode}
                        </div>;
                    }
                },
                {
                    title: this.$t('pages.dataSecondAppearance.appearanceRule'),
                    key: 'importSql',
                    minWidth: 150,
                    ellipsis: true
                }
            ],
            confirmTableHeight: 0
        };
    },
    computed: {
    },
    mounted() {
        window.addEventListener('resize', this.setTableHeight);
        // 延迟一段时间再调用，确保所有元素和样式已渲染完毕
        this.$nextTick(() => {
            this.setTableHeight();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.setTableHeight);
    },
    methods: {
        /**
         * 初始化数据
         * @async
         */
        async initData() {
            this.loading = true;
            await this.getAppearanceTaskData();
            this.loading = false;
        },
        /**
         * 获取上场表数据
         * @async
         */
        async getAppearanceTaskData() {
            try {
                const tablesList = await this.getTablesMetaInfo();

                // 过滤支持二次上场的内存表并映射所需字段
                this.supportAppearTables = tablesList
                    .filter(o => o.isEnabled) // 开启状态
                    .map(item => ({
                        tableName: item.tableName,
                        clusterId: item.clusterId,
                        clusterName: item.clusterName,
                        shardingNo: item.shardingNo,
                        // 同时添加默认规则
                        importMode: 3,
                        importSql: ''
                    }));
            } catch (error) {
                console.error(error);
                this.supportAppearTables = [];
            }
        },
        /**
         * 获取内存表元信息列表
         * @async
         * @returns {Array} 内存表元信息列表
         */
        async getTablesMetaInfo() {
            let tablesList = [];
            try {
                const params = {
                    productId: this.productId
                };
                const res = await getTablesMetaInfo(params);
                // 调用接口获取上场表数据
                if (res.code === '200') {
                    tablesList = res.data || [];
                } else {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
            return tablesList;
        },
        /**
         * 更新选择的上场表
         * @param {Object} tables 包含可选表和选中表的信息
         */
        handleUpdateTables({ availableTables, selectedTables }) {
            this.supportAppearTables = availableTables;
            this.selectedAppearTables = selectedTables;
        },
        /**
         * 更新上场表规则
         * @param {Array} data 更新后的上场表规则数据
         */
        handleUpdateRules(data) {
            this.selectedAppearTables = data;
        },
        /**
         * 执行上场操作
         * @async
         */
        async handleAppearance() {
            this.confirmLoading = true;
            try {
                // 调用执行上场接口
                const params = this.selectedAppearTables.map(item => {
                    return {
                        ...item,
                        productId: this.productId
                    };
                });
                const res = await executeAppearance(JSON.stringify(params));
                if (res.code === '200') {
                    this.$hMessage.success(this.$t('pages.dataSecondAppearance.executingAppearance'));
                    // 跳转至列表页
                    this.$emit('page-jump', 'detail');
                } else if (res?.code?.length === 8) {
                    this.$hMessage.error(this.$t('pages.dataSecondAppearance.appearanceFailed'));
                }
            } catch (err) {
                console.error(err);
            }
            this.confirmLoading = false;
        },
        /**
         * 处理步骤的变更操作
         * @param {String} ope 表示步骤变化的方向('+'表示下一步，'-'表示上一步)
         */
        handleStepChange(ope) {
            if (ope === '+') {
                if (this.currentStep === 0 && !this.selectedAppearTables.length) {
                    this.$hMessage.warning(this.$t('pages.dataSecondAppearance.selectAppearanceTableWarning'));
                    return;
                }
                this.currentStep += 1;
            } else {
                this.currentStep -= 1;
            }
        },
        /**
         * 处理取消操作
         */
        handleCancel() {
            this.$emit('page-jump', 'detail');
        },
        /**
         * 设置表格高度，根据内容框的高度进行计算
         */
        setTableHeight() {
            const boxHeight = this.$refs['content-box']?.offsetHeight;
            this.confirmTableHeight = boxHeight - 80;
        }
    }
};
</script>

<style lang="less" scoped>
.h-icon {
    &:hover {
        cursor: pointer;
    }
}
</style>

<style lang="less" scoped>
@import url("@/components/ustTableVerification/createTask.less");

.create-step {
    position: relative;
    min-width: 1000px;
    height: calc(100% - 44px);

    .content-box {
        overflow: hidden;
    }
}

.step-content {
    height: calc(100% - 60px);
    width: 100%;
    margin-top: 10px;
}

</style>
