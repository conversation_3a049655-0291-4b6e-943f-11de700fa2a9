const NodeRSA = require('node-rsa');
import i18n from '@/locales/i18n';

// 使用后端的公钥（标准PEM格式）
const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxiavlyNmC9SiQpcN80vz
uOEapb3bWOFA5JdltQ6nJ2W0RdrrBmv0vyiv/f63z9PgaDvm2dIanq9+U/I+madA
rUsXVPrIY80PKxhhKGp7ijOrlX3jP7skgS2IaX3/uk3qVX6aDzhZpVnPVFDPhbHk
JmS3paFffDPoog+YKUkAQiH63Csz5P77mgxm1aaRiY2LNr9/P4p7PeZUY37Ao2hu
UKNEdBU4DzVVxPw1Rw1mlcUSL9gW3VX/HpuCn6QBYDBzFSlHZobf0VNdfbBMyvDI
FwRM29ZiQ7FwW8am+nDIMT5HeADxH8SxxYnXLAyHbaOyW8ehnjXlsh5vEBqqREAs
FQIDAQAB
-----END PUBLIC KEY-----`;

/**
 * 使用后端公钥加密（PKCS1填充）
 * @param {string} data - 待加密数据（注意：RSA加密有长度限制，2048位密钥最大加密245字节）
 * @returns {string} 加密后Base64字符串
 */
const encryptData = (data) => {
    try {
        const nodeRSA = new NodeRSA(publicKey);
        // 使用 PKCS1 加密方案
        nodeRSA.setOptions({
            encryptionScheme: 'pkcs1'
        });
        const encrypted = nodeRSA.encrypt(data, 'base64');
        return encrypted;
    } catch (error) {
        console.error(i18n.t('utils.encryptionFailureMessage'), error);
    }
};

// 导出模块方法
module.exports = {
    encryptData
};
