<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="main">
        <div class="title">
            <a-title :title="$getProductType(productInfo.productType)">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        placeholder="请选择"
                        class="title-select"
                        widthAdaption
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="checkProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo">
                            {{ item.productName }}</h-option>
                    </h-select>
                    <div class="split-solid"></div>
                    <h-icon
                        class="icon-setting"
                        name="setup"
                        size="20"
                        color="var(--font-color)"
                        title="配置"
                        @on-click="openConfigDrawer">
                    </h-icon>
                    <div class="title-btn">
                        <h-dropdown
                            trigger="click"
                            transfer
                            customTransferClassName="management-query-dropdown-box"
                            @on-click="handleExportDropdown">
                        <a-button type="dark">
                            导出
                            <h-icon name="unfold"></h-icon>
                        </a-button>
                        <h-dropdown-menu slot="list" >
                            <h-dropdown-item name="batch">
                            批量导出
                            <h-poptip
                                trigger="hover"
                                placement="right"
                                customTransferClassName="apm-poptip monitor-poptip"
                                style="margin-left: 4px;">
                                <h-icon name="prompt1" size="14"></h-icon>
                                <div slot="content" class="pop-content" style="white-space: normal; width: 200px;">
                                    支持批量导出当前产品下的所有管理功能数据
                                </div>
                            </h-poptip>
                            </h-dropdown-item>
                            <h-dropdown-item name="quick">
                            快捷导出
                            <h-poptip
                                trigger="hover"
                                placement="right"
                                style="margin-left: 4px;" customTransferClassName="apm-poptip monitor-poptip">
                                <h-icon name="prompt1" size="14"></h-icon>
                                <div slot="content" class="pop-content" style="white-space: normal; width: 200px;">
                                点击一键导出所选产品管理所有核心的「GetFuncDetailInfo」管理功能
                                </div>
                            </h-poptip>
                            </h-dropdown-item>
                        </h-dropdown-menu>
                        </h-dropdown>
                    </div>
                </slot>
            </a-title>
        </div>
        <menu-layout
            v-if="instanceList.length"
            customMenu
            @menu-fold="menuFold">
            <template v-slot:menu>
                <div class="menu">
                    <div v-show="!menuFoldStatus" style="padding: 10px 5px 0;">
                        <apm-drop-menu-select
                            ref="apm-drop-menu-select"
                            :placeholder="instanceList.length ? '请选择节点': '当前暂无活跃节点'"
                            innerPlaceholder="输入节点名查询"
                            :menuList="dropMenuList"
                            :clearable="false"
                            @confirm="handleMenuConfirm"
                        />
                    </div>
                    <h-input
                        v-show="!menuFoldStatus"
                        v-model.trim="filterText"
                        :clearable="true"
                        placeholder="搜索管理功能"></h-input>
                    <h-menu
                        v-if="filterMenuList.length"
                        ref="menu"
                        :key="filterText"
                        theme="dark"
                        class="apm-management-menu"
                        :active-name="activeName"
                        :open-names="openMenu"
                        style="height: calc(100% - 100px);"
                        :accordion="config.accordion"
                        @on-select="debounceSelectMenuChange">
                        <submenu
                            v-for='item in filterMenuList'
                            :key="item.pluginName"
                            :name="item.pluginName">
                            <template v-slot:title>
                                {{ item.pluginName }}
                            </template>
                            <h-menu-item v-for='child in item.funcs'
                                :key="`${applicationNode}|${item.pluginName}|${child.request.method}|${child.name}`"
                                :class="isOnlyEn ? 'h-menu-cn' : ''"
                                :name="`${applicationNode}|${item.pluginName}|${child.request.method}|${child.name}`">
                                <div v-show="!isOnlyEn" class="text-method ellipsis" :title="`${child.request.method} ${child.name}`">
                                    <span class="text-method-left">{{ child.request.method === 'GET' ? 'G' : 'P'  }}</span>
                                    <span class="text-method-right"> {{ child.name }}</span>
                                </div>
                                <div v-show="isOnlyEn" class="text-method ellipsis" :title="`${child.request.method} ${child.name} \n${child.chineseName || '暂无'}`" >
                                    <span class="text-method-left text-method-left-chi">{{ child.request.method === 'GET' ? 'G' : 'P'  }}</span>
                                    <span class="text-method-right"> {{ child.name }}</span>
                                    <div class="ellipsis" style="color: #9296a1; margin-left: 30px;">{{  child.chineseName || '暂无'}}</div>
                                </div>
                            </h-menu-item>
                        </submenu>
                    </h-menu>
                    <p
                        v-show="!filterMenuList.length && !menuFoldStatus"
                        style="
                            width: 100%;
                            font-size: 12px;
                            text-align: center;
                            top: 100px;
                            color: rgb(82 86 96);">
                        未查询到管理功能
                    </p>
                    <h-checkbox
                        v-show="filterMenuList.length && !menuFoldStatus"
                        v-model="isOnlyEn"
                        class="menu-footer"
                        :label="true"
                    >
                        <span style="color: #fff;">显示"中文译名"</span>
                    </h-checkbox>
                </div>
            </template>
            <template v-slot:right>
               <div v-show="activeName" class="box">
                    <div class="select-box">
                        <management-box
                            ref="managementBox"
                            :requestUrl="url"
                            :managementData="managementData"
                            :manageApiMeta="manageApiMeta"
                            @query="getManageInfo"
                            @edit="editManageInfo"
                            @fet-Height="fetTableHeight"></management-box>
                    </div>
                    <div v-show="tabs.length" ref="table-box" class="result-box">
                        <span class="headers">{{ headers.statusCode }}&nbsp;
                            <template v-if="apmTime === totalTime">{{ totalTime }}&nbsp;</template>
                            <h-poptip
                                v-else
                                trigger="hover"
                                placement="left"
                                class="apm-poptip-time"
                                transfer
                                customTransferClassName="apm-poptip apm-poptip-time monitor-poptip">
                                {{ totalTime }}&nbsp;

                                <div slot="content" class="pop-content" style='white-space: normal;'>
                                    <div>
                                        <span class="time-item-label">整体耗时：</span>
                                        {{ totalTime }}
                                        <div>
                                            <span class="time-item-label">APM耗时：</span>
                                            {{ apmTime }}
                                        </div>
                                        <div>
                                            <span class="time-item-label">应用节点耗时：</span>
                                            {{ manageDurationTime }}
                                        </div>
                                    </div>
                                </div>
                            </h-poptip>
                            {{ headers.contentLength }}
                            <div
                                v-show="isExistTab(tabName)"
                                style="display: inline-block;
                                color: #647495;">｜</div>
                            <h-radio-group
                                v-show="isExistTab(tabName)"
                                v-model="tabShowType"
                                type="button"
                                size="small"
                                @on-change="handleChangeTabShowType">
                                <h-radio label="JSON" :disabled="loading2">Json</h-radio>
                                <h-radio label="Table" :disabled="loading2">Table</h-radio>
                            </h-radio-group>
                        </span>
                        <h-tabs
                            ref="tabs"
                            v-model="tabName"
                            type="card"
                            :isRemoveTab="false"
                            :animated="false"
                            :closable="tabs.length > 1"
                            @on-click="tabClick(tabName)"
                            @on-before-tab-remove="handleTabRemove">
                            <h-tab-pane
                                v-for="tab in (maxTabNum ? tabs.slice(0, maxTabNum)
                                    : tabs ||
                                    [])"
                                :key="tab.key"
                                :name="tab.key"
                                :label="label(tab)"
                            >
                            </h-tab-pane>
                            <div v-show="maxTabNum" slot="extra">
                                <h-dropdown
                                    trigger="click"
                                    :transfer="true"
                                    customTransferClassName="management-query-dropdown-box"
                                    @on-click="handleTabsSelect">
                                    <h-button
                                        type="text"
                                        size="small">
                                        <h-icon
                                            name="ios-more"
                                            size="18"
                                            color="var(--font-color)">
                                        </h-icon>
                                    </h-button>
                                    <h-dropdown-menu slot="list" class="custom-dropdown-menu">
                                        <div class="dropdown-scroll-list">
                                            <h-dropdown-item
                                                v-for="tab in tabs.slice(maxTabNum)"
                                                :key="tab.key"
                                                :name="tab.key"
                                                :title="getTitlePoptip(tab)">
                                                <div class="text">{{tab.name}}</div>
                                                <h-icon
                                                    name="android-close"
                                                    size="18"
                                                    color="var(--error-color)"
                                                    @on-click="handleRemove(tab.key)"></h-icon>
                                            </h-dropdown-item>
                                        </div>
                                        <div class="dropdown-close-btn">
                                            <h-button
                                                type="text"
                                                size="small"
                                                @click="handleAllRemove">关闭全部</h-button>
                                        </div>
                                    </h-dropdown-menu>
                                </h-dropdown>
                            </div>
                        </h-tabs>
                        <div class="tab-body">
                            <a-loading v-if="loading2"></a-loading>
                            <div
                                v-if="!loading2 && tabShowType === 'Table' && jsonData[tabName]"
                                ref="tab-table"
                                class="table"
                                :style="{ height: tableHeight + 'px', overflow: 'auto' }">
                                <!-- 使用JsonPathTable组件展示表格 -->
                                <div v-if="jsonPathList[tabName] && jsonPathList[tabName].length > 0">

                                    <!-- 遍历jsonPathList展示多个表格 -->
                                    <div v-for="(item, index) in jsonPathList[tabName]" :key="index">
                                        <json-path-table
                                            :title="tabName.split('|')[3] + ' / ' +item.aliasName + ' ( ' + item.jsonPath + ' ) '"
                                            :json-data="jsonData[tabName] || {}"
                                            :json-path="item.jsonPath"
                                            :header-alias="headerAlias"
                                            :sortable="item.sortable"
                                        />
                                    </div>

                                    <!-- 剔除所有jsonPath路径后的表格 -->
                                    <json-path-table
                                        :title="tabName.split('|')[3] + ' / '+'其他数据'"
                                        :json-data="mainTableData[tabName] || {}"
                                        json-path=""
                                        :header-alias="headerAlias"
                                        :sortable="false"
                                    />
                                </div>
                                <!-- 如果没有配置jsonPath，直接显示全部数据  减去表头和其他元素的高度 -->
                                <div v-else>
                                    <json-path-table
                                        hasQlobal
                                        :title="tabName.split('|')[3]"
                                        :json-data="mainTableData[tabName] || {}"
                                        json-path=""
                                        :header-alias="headerAlias"
                                        :sortable="true"
                                        :tableHeight="tableHeight - 60"
                                    />
                                </div>
                            </div>
                            <div
                                v-if="!loading2 && tabShowType === 'JSON' && jsonData[tabName]"
                                class="json-box"
                                :style="{ height: tableHeight + 'px' }">
                                <json-viewer
                                    v-if="!loading2 && !isShowTextFunc(jsonData[tabName])"
                                    :value="jsonData[tabName] || {}"
                                    :expand-depth="10"
                                    copyable
                                    :expanded="true"></json-viewer>
                                <codemirror
                                    v-if="!loading2 && isShowTextFunc(jsonData[tabName])"
                                    ref="codeEditor"
                                    class="json-str-viewer"
                                    :options="editorOptions"
                                    :value="JSON.stringify(jsonData[tabName], null, 4)"></codemirror>
                            </div>
                            <no-data v-if="isExistTab(tabName) && !loading2 && !jsonData[tabName]"></no-data>
                            <no-data v-if="!isExistTab(tabName)" text="请选择管理功能手动发起请求"></no-data>
                        </div>
                    </div>
                    <no-data v-show="!tabs.length" text="请选择管理功能手动发起请求"></no-data>
                </div>
                <no-data v-if="applicationNode && !activeName" class="box" text="请选择管理功能"></no-data>
                <no-data v-if="!applicationNode && !activeName" class="box"></no-data>
            </template>
        </menu-layout>
        <div v-else style="height: calc(100% - 80px);">
            <no-data></no-data>
        </div>
        <a-loading v-if="loading"></a-loading>
        <!-- 导出弹窗 -->
        <export-data-modal v-if="modalInfo.status" :modalData="modalInfo" />
        <!-- 批量导出 -->
        <batch-export-modal
            v-if="batchExportInfo.status"
            :modalData="batchExportInfo"
            :productId="productInstNo" />
        <!-- 配置说明 -->
        <config-data-drawer
            v-if="configDrawerVisible.status"
            :modalInfo="configDrawerVisible"
            :config="config"
            @config-change="handleConfigChange"
        />
        <!-- JSONPATH配置 -->
        <json-path-drawer
            v-if="jsonPathInfo.status"
            :modalInfo="jsonPathInfo"
            @update="updateJsonPathList"
        />
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { JSONPath } from 'jsonpath-plus';
import { getManageMetas, getManageInfo, editManageInfo, getMonitorHeartbeats, getClusterRoles, downloadFuncDetailInfoData } from '@/api/httpApi';
import { getProductInstances, getMachineRoomInfo, getManageMeta } from '@/api/productApi';
import jsonViewer from 'vue-json-viewer';
import apmDropMenuSelect from '@/components/common/apmDropMenuSelect/apmMdbSqlDropMenu';
import managementBox from '@/components/managementQuery/managementBox';
import batchExportModal from '@/components/managementQuery/batchExportModal';
import noData from '@/components/common/noData/noData';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import exportDataModal from '@/components/managementQuery/exportDataModal';
import configDataDrawer from '@/components/managementQuery/configDataDrawer';
import jsonPathDrawer from '@/components/managementQuery/jsonPathDrawer';
import JsonPathTable from '@/components/managementQuery/JsonPathTable';
import { getDBInstance } from '@/utils/indexedDBInstance';
import { getSpanWidth, getByteSize, getObjByArray, getCurrentDatetime } from '@/utils/utils';
import {  setMenuList } from '@/utils/roomClusterNode';
import { defaultJsonPathConfig } from '@/config/defaultJsonPath';
import { transformSchema } from '@/components/managementQuery/schema-parser';

import { codemirror } from 'vue-codemirror';
// theme css
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/ayu-mirage.css';
import 'codemirror/theme/xq-light.css';
// JSON代码高亮需要由JavaScript插件支持
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/theme/idea.css';
import 'codemirror/keymap/sublime.js';
import 'codemirror/addon/dialog/dialog.js';
import 'codemirror/addon/dialog/dialog.css';
import 'codemirror/addon/search/search';
import 'codemirror/addon/search/searchcursor.js';
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/comment-fold.js';
import 'codemirror/addon/display/fullscreen.css';
import 'codemirror/addon/display/fullscreen.js';
import 'codemirror/addon/edit/matchbrackets.js';
import 'codemirror/addon/edit/closebrackets.js';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/anyword-hint.js';
import 'codemirror/addon/comment/comment.js';
import 'codemirror/addon/fold/indent-fold.js';
import 'codemirror/addon/fold/markdown-fold.js';
import 'codemirror/addon/fold/xml-fold.js';
// highlightSelectionMatches
import 'codemirror/addon/scroll/annotatescrollbar.js';
import 'codemirror/addon/search/matchesonscrollbar.js';
import 'codemirror/addon/search/match-highlighter.js';
// keyMap
import 'codemirror/mode/clike/clike.js';

import '@/assets/css/poptip-1.less';

export default {
    data() {
        return {
            db: null,
            isOnlyEn: false, // 是否中英文翻译
            filterText: '', // 过滤管理功能
            productInfo: {},
            openMenu: [], // 默认展开
            activeName: '', // menu默认选择
            menuList: [],
            productInstNo: '',
            url: '',
            jsonData: {},
            applicationName: '',
            applicationNode: '',
            heartList: [],
            managementData: {},
            manageApiMeta: {},
            loading: false,
            loading2: false,
            downLoading: false,
            isFirstRender: true,
            headers: {
                contentLength: '',
                statusCode: ''
            },
            totalTime: '',
            manageDurationTime: '', // 管理功能执行耗时
            apmTime: '',
            instanceList: [],
            tableHeight: 0,
            menuFoldStatus: false,
            tabName: '',
            tabs: [],
            // 默认配置
            config: {},
            configDrawerVisible: {
                status: false
            },
            tabShowType: '',
            tabShowTypeList: {},
            maxTabNum: 0,
            editorOptions: {
                // codemirror options
                tabSize: 4,
                mode: 'application/json',
                theme: 'ayu-mirage',
                fullScreen: false,
                lineNumbers: true,
                line: true,
                // 代码折叠
                foldGutter: true,
                gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
                // 高级配置（需要引入对应的插件包）,codemirror advanced options(You need to manually introduce the corresponding codemirror function script code)
                // sublime、emacs、vim三种键位模式，支持你的不同操作习惯
                keyMap: 'sublime',
                showHitObj: null, // 延迟显示联想列表
                currentWord: '', // 关键字记录
                updateTimer: null,
                viewportMargin: 100,
                maxHighlightLength: 1000,
                readOnly: true,
                hintOptions: {
                    // 当匹配只有一项的时候是否自动补全
                    completeSingle: false
                },
                runCur: null
            },
            roomTableData: [], // 机房信息
            clusterRoles: {}, // 心跳
            dropMenuList: [], // 分类数据
            modalInfo: {
                status: false,
                managementList: []
            },
            batchExportInfo: { // 批量导出配置
                status: false
            },
            nodeUrlMap: {}, // 新增：缓存打开过的节点的url
            label: (item) => (h) => {
                const popTab = this.getTitlePoptip(item);
                return h('span', {
                    attrs: {
                        title: popTab
                    }
                }, item.name);
            },
            // JSONPATH配置
            jsonPathInfo: {
                status: false
            },
            jsonPathList: {},
            mainTableData: {},
            headerAlias: []
        };
    },
    async mounted() {
        this.db = await getDBInstance();
        this.db.clear('management-query');
        // 初始化默认的jsonpath配置
        // await this.initDefaultJsonPath();
        this.loading = true;
        await this.getProductList({ filter: 'excludeLdpApm' });
        const productInstNo = localStorage.getItem('productInstNo');
        this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        this.loading = false;
        window.addEventListener('resize', this.resize);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    },
    computed: {
        ...mapState({
            productList: state => {
                return _.filter(state.product.productListLight, o => o.productType !== 'NSQ1.0') || [];
            }
        }),

        filterMenuList() {
            // 合并map和filter，减少遍历
            return this.menuList.reduce((acc, plugin) => {
                const list = plugin.funcs || [];
                list.sort((a, b) => {
                    if (a?.request?.method === 'GET' && b?.request?.method === 'POST') return -1;
                    if (a?.request?.method === 'POST' && b?.request?.method === 'GET') return 1;
                    return a.name.localeCompare(b.name);
                });
                const filteredFuns = list.filter(fun =>
                    String(fun.name).toLowerCase().includes(String(this.filterText).toLowerCase())
                );
                if (filteredFuns.length > 0) {
                    acc.push({ ...plugin, funcs: filteredFuns });
                }
                return acc;
            }, []);
        }
    },
    watch: {
        filterMenuList(newVal) {
            const list = this.tabName?.split('|');
            this.activeName = this.tabName;
            this.openMenu = list?.[1] ? [list?.[1]] : [];
            this.$nextTick(() => {
                try {
                    this.$refs['menu'] && this.$refs['menu'].updateActiveName();
                } catch (e) {}
            });
        },
        filterText() {
            this.openDefaultMenu();
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化默认jsonpath配置
        async initDefaultJsonPath() {
            try {
                for (const [key, value] of Object.entries(defaultJsonPathConfig)) {
                    // 检查是否已存在配置
                    const existingConfig = await this.db.get(key, 'management-jsonpath');
                    if (!existingConfig) {
                        // 不存在则保存默认配置
                        await this.db.set(value, key, 'management-jsonpath');
                    }
                }
            } catch (error) {
                console.warn('初始化默认jsonPath配置失败:', error);
            }
        },
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                this.tableHeight = this.$refs['table-box']?.getBoundingClientRect().height - 60;
            });
        },
        resize(){
            this.maxTabNum = this.getMaxTabNum();
            this.fetTableHeight();
        },
        // 清理数据
        clearData() {
            this.filterText = '';
            this.productInfo = {};
            this.activeName = '';
            this.openMenu = [];
            this.managementData = {};
            this.manageApiMeta = {};
            this.jsonData = {};
            this.mainTableData = {};
            this.jsonPathList = {};
            this.headerAlias = [];
            this.resetPerformanceTime();
            this.menuList = [];
            this.tabName = '';
            this.tabs = [];
            this.tableHeight = 0;
            this.instanceList = [];
            this.config = {
                showType: 'Table',
                accordion: true,
                saveLastParam: true,
                resultLink: true,
                autoGetSend: true,
                maxTabNums: 20,
                ...JSON.parse(localStorage.getItem('ldpManageConfig'))
            };
            this.tabShowType = this.config.showType;
            this.maxTabNum = 0;
            this.roomTableData = [];
            this.clusterRoles = {};
            this.dropMenuList = [];
            this.nodeUrlMap = {};
        },

        // ---------------------------------------------------------------- 节点菜单 --------------------------------------------------------------
        handleMenuConfirm(node){
            this.applicationName = node?.[0]?.label;
            this.applicationNode = node?.[0]?.value;
            this.$nextTick(() => {
                this.handleChangeNode(this.applicationNode);
            });
        },
        // 查询menu列表
        handleMenuQuery(){
            this.dropMenuList = setMenuList(this.instanceList, this.roomTableData, this.clusterRoles, this.$store?.state?.apmDirDesc?.appTypeDictDesc);
            if (this.instanceList.length) {
                const node = this.isFirstRender ? _.find(this.instanceList, ['id', this.$route.query.instanceId]) || this.dropMenuList?.[0]?.groupList?.[0]?.subGroupList?.[0]?.nodes?.[0] : this.dropMenuList?.[0]?.groupList?.[0]?.subGroupList?.[0]?.nodes?.[0];
                this.$nextTick(() => {
                    node && this.$refs?.['apm-drop-menu-select'] && this.$refs['apm-drop-menu-select'].setSelectMenu([{
                        label: node?.instanceName || node?.label,
                        value: node.id || node?.value
                    }]);
                });
            }
        },
        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.productInfo.instances = res?.data?.instances;
            }
        },
        // 计算instanceList
        computerInstanceList() {
            const list = (this.productInfo.instances || []).filter(obj1 =>
                this.heartList.some(obj2 =>
                    obj2.id === obj1.id)) || [];

            this.instanceList = list;
        },
        // 获取机房信息
        async getMachineRoomInfo() {
            const res = await getMachineRoomInfo({ productId: this.productInstNo });
            if (res.code === '200') {
                this.roomTableData = res?.data || [];
            }
        },
        // 获取集群角色
        async getClusterRoles() {
            const param = {
                productId: this.productInstNo
            };
            const res = await getClusterRoles(param);
            if (res?.code === '200'  && Array.isArray(res.data)) {
                this.clusterRoles = getObjByArray(res.data, 'instanceId');
            }
        },

        // 切换产品
        async checkProduct(e) {
            this.loading = true;
            this.clearData();
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                const list = await this.getMonitorHeartbeats();
                list.forEach((ele, idx) => {
                    this.$set(this.heartList, idx, ele);
                });
                await this.getMachineRoomInfo();
                await this.getClusterRoles();
                await this.getProductInstances();
                this.computerInstanceList();

                this.$nextTick(() => {
                    this.handleMenuQuery();
                });
            }
            this.loading = false;
        },

        // 获取当前instanceId
        getCurrInstanceId(key) {
            const arr = key?.split('|');
            return arr?.[0] || '';
        },
        // 查询管理功能接口，优先用currentTabNode
        getManageInfo(param) {
            this.loading2 = true;
            param.productId = this.productInfo.id;
            param.instanceId = this.getCurrInstanceId(this.tabName);
            const name = this.tabName;
            this.tabShowType = this.tabShowTypeList[name] || this.config.showType;

            const data = this.getFunDataByKey(name);
            if (!this.isExistTab(name)) {
                if (this.tabs.length >= this.config.maxTabNums) {
                    this.tabs.pop(); // 超过this.config.maxTabNums，自动剔除最后一个tab
                }
                this.tabs.unshift({
                    key: name,
                    ...data
                });
                this.maxTabNum = this.getMaxTabNum();
            }
            const startTime = Date.now();
            getManageInfo(param).then(async res => {
                const { data, tracing } = res;
                if (data && this.tabName === name) {
                    const duration = (tracing || []).find(item => item.spanName === 'appApmManagementExitLinkLatency')?.duration;
                    const apmTime = (tracing || []).find(item => item.spanName === 'appApmManagementEnterLinkLatency')?.duration;
                    await this.processResponseWithJsonPath(data, { totalTime: Date.now() - startTime, duration, apmTime });
                }
                this.$nextTick(() => {
                    this.loading2 = false;
                });
            }).catch(err => {
                this.loading2 = false;
            });
            this.$nextTick(() => {
                this.setSaveLastParam();
                this.fetTableHeight();
            });
        },
        // 修改管理功能接口，优先用currentTabNode
        editManageInfo(param) {
            this.loading2 = true;
            param.productId = this.productInfo.id;
            param.instanceId = this.getCurrInstanceId(this.tabName);

            const name = this.tabName;
            this.tabShowType = this.tabShowTypeList[name] || this.config.showType;

            const data = this.getFunDataByKey(name);
            if (!this.isExistTab(name)) {
                if (this.tabs.length >= this.config.maxTabNums) {
                    this.tabs.pop(); // 超过20，自动剔除最后一个tab
                }
                this.tabs.unshift({
                    key: name,
                    ...data
                });
                this.maxTabNum = this.getMaxTabNum();
            }
            const startTime = Date.now();
            editManageInfo(param).then(async res => {
                const { data, tracing } = res;
                if (data && this.tabName === name) {
                    const duration = (tracing || []).find(item => item.spanName === 'appApmManagementExitLinkLatency')?.duration;
                    const apmTime = (tracing || []).find(item => item.spanName === 'appApmManagementEnterLinkLatency')?.duration;
                    await this.processResponseWithJsonPath(data, { totalTime: Date.now() - startTime, duration, apmTime });
                }
                this.$nextTick(() => {
                    this.loading2 = false;
                });
            }).catch(err => {
                this.loading2 = false;
            });
            this.$nextTick(() => {
                this.setSaveLastParam();
                this.fetTableHeight();
            });
        },
        // 配置变化
        handleConfigChange(val, key){
            this.config = {
                ...this.config,
                ...JSON.parse(localStorage.getItem('ldpManageConfig')),
                [key]: val
            };
            localStorage.setItem('ldpManageConfig', JSON.stringify(this.config));
            if (key === 'showType'){
                this.tabShowType = this.config.showType;
                this.tabShowTypeList = {};
            }
            if (key === 'saveLastParam' && val){
                this.$hMessage.info('如需保存当前选中功能号输入参数，请手动触发一次查询请求！');
                return;
            }
            if (key === 'resultLink' && val){
                if (!this.tabName) return;
                const list = this.tabName.split('|');
                this.url = this.nodeUrlMap[list[0]];
                if (this.applicationNode !== list[0]) {
                    this.applicationNode = list[0];
                    const node = this.instanceList.find(item => item.id === list[0]);
                    if (node && this.$refs['apm-drop-menu-select']) {
                        this.$refs['apm-drop-menu-select'].setSelectMenu([{
                            label: node.instanceName || node.label,
                            value: node.id || node.value
                        }]);
                    }
                }
                this.activeName = this.tabName;
                this.openMenu = [list[1]];
                return;
            }
            if (key === 'accordion' && val){
                const list = this.activeName.split('|');
                this.openMenu = [list[1]];
                return;
            }
            if (key === 'maxTabNums'){
                if (this.tabs.length > this.config.maxTabNums){
                    this.handleAllRemove();
                }
            }
        },
        // 改变参数数据
        async changeManagementData(data, name){
            if (!data) return;
            this.tabName = name;
            this.url = this.nodeUrlMap[this.getCurrInstanceId(this.tabName)];
            const saveParam = await this.getSaveLastParam();
            this.managementData = {
                ...data,
                request: {
                    ...data.request,
                    params: saveParam?.length ? [...saveParam] : [...data.request.params]
                }
            };

            // 获取配置元信息
            this.getManagementQueryMeta(name);
        },
        // 获取配置元信息
        async getManagementQueryMeta(name){
            const arr = name?.split('|');
            const params = {
                productId: this.productInfo.id,
                funcName: arr[3],
                pluginName: arr[1],
                instanceId: arr[0]
            };
            const res = await getManageMeta(params);
            if (res.code === '200'){
                this.manageApiMeta = res?.data || {};
                this.headerAlias = transformSchema(this.manageApiMeta?.schema?.response || {});
            } else {
                this.manageApiMeta = {};
                this.headerAlias = [];
            }
        },
        // 清空耗时信息
        resetPerformanceTime() {
            this.headers = {};
            this.totalTime = '';
            this.apmTime = '';
            this.manageDurationTime = '';
        },
        // 改变交互数据
        async handleDataChange(data, whetherSaveHistory = false){
            if (!data) return;
            // tab切换-tab历史值
            this.resetPerformanceTime();
            this.jsonData[this.tabName] = this.jsonData?.[this.tabName] || '';
            this.mainTableData[this.tabName] = this.jsonData?.[this.tabName] || '';
            await this.handleChangeTabShowType(this.tabShowType);
            if (whetherSaveHistory) return;

            // 菜单选择-清空数据、重新请求
            this.$nextTick(() => {
                data.request.method === 'GET' && this.clearTableJsonByKey();
                data.request.method === 'GET' && this.$refs['managementBox'].handleSend();
            });
        },
        debounceSelectMenuChange: _.debounce(function (name) {
            this.selectMenuChange(name);
        }, 500),
        // 导航栏切换
        async selectMenuChange(name) {

            this.tabShowType = this.tabShowTypeList[name] || this.config.showType;
            this.activeName = name;
            const data = this.getFunDataByKey(name);
            // 改变参数数据
            await this.changeManagementData(data, name);
            // 是否自动get请求
            if (!this.config.autoGetSend) {
                if (this.isExistTab(name)) {
                    this.moveHiddenTabToFirst(name);
                    await this.tabClick(name);
                }
                this.resetPerformanceTime();
                return;
            } else {
                this.moveHiddenTabToFirst(name);

                if (data.request.method === 'GET' && !this.isExistTab(name)) {
                    if (this.tabs.length >= this.config.maxTabNums) {
                        this.tabs.pop(); // 超过20，自动剔除最后一个tab
                    }
                    this.tabs.unshift({
                        key: name,
                        ...data
                    });
                    this.maxTabNum = this.getMaxTabNum();
                }
            }
            // 改变交互数据
            await this.handleDataChange(data);
            this.fetTableHeight();
        },
        // 清理table、json数据
        clearTableJsonByKey(key) {
            this.mainTableData[this.tabName] = '';
            this.jsonData[this.tabName] = '';
            this.jsonPathList[this.tabName] = [];
            this.headerAlias = [];
            this.resetPerformanceTime();
        },
        // 判断tab是否存在
        isExistTab(key) {
            const res = this.tabs.filter(v => v.key === key);
            return res.length;
        },
        // 切换tab时，右侧管理功能区跟随tab key里的instanceId
        async tabClick(name) {
            this.loading2 = true;
            this.tabShowType = this.tabShowTypeList[name] || this.config.showType;
            const arr = name.split('|');
            const instanceId = arr?.[0] || '';
            // 新增url同步逻辑
            this.url = this.nodeUrlMap[instanceId];
            try {
                if (this.config.resultLink) {
                    if (this.applicationNode !== instanceId) {
                        this.applicationNode = instanceId;
                        const node = this.instanceList.find(item => item.id === instanceId);
                        if (node && this.$refs['apm-drop-menu-select']) {
                            this.$refs['apm-drop-menu-select'].setSelectMenu([{
                                label: node.instanceName || node.label,
                                value: node.id || node.value
                            }]);
                        }
                    }
                    this.activeName = name;
                    this.openMenu = arr?.[1] ? [arr?.[1]] : [];
                }
                const data = this.getFunDataByKey(name);
                await this.changeManagementData(data, name);
                await this.handleDataChange(data, true);
            } finally {
                this.fetTableHeight();
                this.$nextTick(() => {
                    this.loading2 = false;
                });
            }
        },
        // dropDown选择
        async handleTabsSelect(name){
            this.moveHiddenTabToFirst(name);
            await this.tabClick(name);
        },
        // dropDown删除
        handleRemove(key){
            this.tabName = '';
            const targetIndex = this.tabs.findIndex(item => item.key === key);
            this.handleTabRemove(targetIndex, this.tabs?.[targetIndex]?.key || '');
        },
        // 删除全部
        handleAllRemove(){
            this.tabs = [];
            this.tabShowTypeList = {};
            this.jsonData = {};
            this.mainTableData = {};
            this.jsonPathList = {};
            this.headerAlias = [];
            this.resetPerformanceTime();
            this.maxTabNum = this.getMaxTabNum();
        },
        // tab删除
        handleTabRemove(index, name) {
            delete this.tabShowTypeList[name];
            delete this.jsonData[name];
            delete this.mainTableData[name];
            delete this.jsonPathList[name];
            this.tabs.splice(index, 1);
            this.resetPerformanceTime();
            this.maxTabNum = this.getMaxTabNum();
            this.$nextTick(async () => {
                this.tabs?.[0]?.key && await this.tabClick(this.tabs[0].key);
            });
        },
        // 判断元素是否被隐藏、移动到首位
        moveHiddenTabToFirst(name){
            const targetIndex = this.tabs.findIndex(item => item.key === name);
            if (this.maxTabNum && targetIndex !== -1 && targetIndex >= this.maxTabNum) {
                const targetObject = this.tabs.splice(targetIndex, 1)[0];
                this.tabs.unshift(targetObject);
                this.maxTabNum = this.getMaxTabNum();
            }
        },
        // 保存数据库数据
        async setSaveLastParam() {
            if (this.config.saveLastParam) {
                const key = [this.productInstNo, this.tabName].join('@');
                const params = this.$refs['managementBox'].formDynamic.items;
                this.db.set(params, key, 'management-query');
            }
        },
        // 获取数据库数据
        async getSaveLastParam() {
            const key = [this.productInstNo, this.tabName].join('@');
            const param = await this.db.get(key, 'management-query');
            return param || [];
        },
        // 获取可见tab最大个数
        getMaxTabNum() {
            const tabsRef = this.$refs.tabs;
            let totalWidth = 0;
            let num = 0;
            if (tabsRef && tabsRef?.$el?.querySelectorAll('.h-tabs-nav-container')?.[0]){
                const navWidth = tabsRef.$el.querySelectorAll('.h-tabs-nav-container')[0]?.offsetWidth - 10;
                for (const [index, tab] of Object.entries(this.tabs)){
                    const width = getSpanWidth(tab.name, '14px') + 40;
                    totalWidth += width;
                    if (totalWidth > navWidth) {
                        num = index - 1;
                        break;
                    }
                }
            }
            return num < 0 ? 0 : num;
        },
        // 切换应用节点
        handleChangeNode(name) {
            this.activeName = '';
            this.openMenu = [];
            this.menuList = [];
            this.applicationNode = name;
            name && this.getManageMetas();
        },
        // 获取元数据信息
        getManageMetas() {
            const param = {
                productId: this.productInfo.id,
                instanceId: this.applicationNode
            };
            getManageMetas(param).then(res => {
                this.activeName = '';
                this.menuList = [];
                const { data } = res;
                if (data) {
                    Array.isArray(data.plugins) && data.plugins.forEach((ele, idx) => {
                        this.$set(this.menuList, idx, ele);
                    });
                    // 新增：tabClick 里加上 url 同步逻辑, 用 nodeUrlMap 做缓存
                    if (this.applicationNode) {
                        this.nodeUrlMap[this.applicationNode] = data.url;
                        this.url = this.nodeUrlMap[this.getCurrInstanceId(this.tabName)];
                        this.activeName = this.tabName;
                        this.openMenu = this.tabName?.split('|')?.[1] ? [this.tabName?.split('|')?.[1]] : [];
                        this.$nextTick(() => {
                            try {
                                this.$refs['menu'] && this.$refs['menu'].updateActiveName();
                            } catch (e) {}
                        });
                    }
                    // 初始化界面调时用
                    this.jumpFuncName();
                }
            }).catch(err => {
                this.loading = false;
                this.menuList = [];
                this.isFirstRender = false;
            });
        },
        // 获取初始功能信息
        jumpFuncName(){
            // 默认打开
            const firstMenu = this.isFirstRender ? _.find(this.filterMenuList, ['pluginName', this.$route.query.pluginName]) : '';
            const firstFunc = this.isFirstRender ? _.find(firstMenu?.funcs || [], ['name', this.$route.query.funcName]) : '';
            if (firstMenu && firstFunc) {
                setTimeout(async () => {
                    this.openMenu = [firstMenu?.pluginName];
                    await this.selectMenuChange(`${this.applicationNode}|${firstMenu?.pluginName}|${firstFunc?.request?.method}|${firstFunc?.name}`);
                }, 500);
            }
            this.isFirstRender = false;
        },
        // 获取心跳数据
        getMonitorHeartbeats() {
            const param = {
                productId: this.productInfo.id,
                type: 'instance'
            };
            return new Promise((resolve, reject) => {
                getMonitorHeartbeats(param).then(res => {
                    const { data } = res;
                    const list = Array.isArray(data) ? _.filter(data, obj => obj.status !== 'stop') : [];
                    resolve(list);
                }).catch(error => {
                    reject(error);
                });
            });
        },
        // 菜单展开收起状态
        menuFold(status) {
            this.menuFoldStatus = status;
            setTimeout(() => {
                this.maxTabNum = this.getMaxTabNum();
            }, 500);
        },
        // 计算json大小，判断文本展示类型
        isShowTextFunc(data) {
            const jsonStr = JSON.stringify(data);
            if (getByteSize(jsonStr) > 600 * 1024) {
                return true;
            }
            return false;
        },
        // 根据key值获取管理功能信息，this.menuList当前左侧列表
        getFunDataByKey(key) {
            const arr = key.split('|');
            const pluginName = arr[1];
            const method = arr[2];
            const funcName = arr[3];

            const list = [...this.menuList] || [];

            // 在左侧列表找对应管理功能的基础信息、没有在tabs里找历史保存的数据
            const funcList = _.find(list, ['pluginName', pluginName])?.funcs ;
            if (!funcList?.length){
                return this.tabs?.find(item => item.key === key) || [];
            }
            for (const item of funcList) {
                if (item.name === funcName && item?.request?.method === method) {
                    return {
                        ...item
                    };
                }
            }
            return null;
        },
        // 改变所选中tab展示的类型  表格 还是 json
        async handleChangeTabShowType(value){
            this.loading2 = true;
            this.tabShowType = value;
            this.tabShowTypeList[this.tabName] = this.tabShowType;
            // const jsonPathList = await this.getJsonPathList(this.tabName);
            // this.$set(this.jsonPathList, this.tabName, [...jsonPathList]);

            // this.$nextTick(() => {
            //     if (value === 'Table') {
            //         // 如果有jsonPath配置，处理主表格数据
            //         if (jsonPathList && jsonPathList.length > 0) {
            //             // 使用 deleteAllJsonPaths 处理数据
            //             const processedData = this.deleteAllJsonPaths(this.jsonData[this.tabName], jsonPathList);
            //             this.$set(this.mainTableData, this.tabName, processedData);
            //         } else {
            //             this.$set(this.mainTableData, this.tabName, this.jsonData[this.tabName]);
            //         }
            //     }
            // });
            this.loading2 = false;
        },

        /**
         * 根据搜索结果默认展开插件，展开全部or展开第一个取决于交互配置
         */
        openDefaultMenu() {
            let openMenu;
            const menuListLength = this.filterMenuList.length;
            const isOpenAll = !this.config.accordion;
            if (this.filterText && menuListLength > 0) {
                // 若有搜索内容，则展开插件
                openMenu = isOpenAll ? this.filterMenuList.map(item => item.pluginName) : [this.filterMenuList[0].pluginName];
            } else if (this.activeName && menuListLength > 0) {
                // 没有搜索内容但有上次选中的插件，则恢复选中
                const plugin = this.filterMenuList.find(({ pluginName, funcs }) =>
                    funcs.length && funcs.some(func =>
                        `${this.applicationNode}|${pluginName}|${func.request?.method}|${func.name}` === this.activeName
                    )
                );
                if (plugin) openMenu = [plugin.pluginName];
            }
            this.openMenu = openMenu;
        },

        // --------------------------------------------------------------  顶部配置 --------------------------------------------------------------------------------
        // 打开配置
        openConfigDrawer() {
            this.configDrawerVisible.status = true;
        },
        // 导出
        handleExportDropdown(name) {
            if (name === 'batch') {
                this.handleSelectExport();
            } else if (name === 'quick') {
                this.handleAllExport();
            }
        },
        // 打开批量导出弹窗
        handleSelectExport() {
            this.batchExportInfo.status = true;
        },
        // 导出所有核心GetFuncDetailInfo数据
        async handleAllExport() {
            const param = {
                productId: this.productInstNo,
                pluginName: 'ldp_bizproc',
                funcName: 'GetFuncDealInfo',
                instanceIdentities: [
                    'bizproc'
                ]
            };
            this.$hMsgBoxSafe.confirm({
                title: '下载',
                content: `您确定下载所有核心节点GetFuncDetailInfo管理功能数据？`,
                onOk: async () => {
                    try {
                        this.downLoading = true;
                        const res = await downloadFuncDetailInfoData(param);
                        const objUrl = window.URL.createObjectURL(new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
                        // 通过创建a标签实现
                        const link = document.createElement('a');
                        link.href = objUrl;

                        // 拼接成完整的时间字符串
                        const formattedDateTime = getCurrentDatetime();
                        // 对下载的文件命名
                        link.setAttribute('download', `${this.productInstNo}-ldp_bizproc-FuncDetailInfo-${formattedDateTime}.csv`);
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(objUrl);
                    } catch (err) {
                        console.error(err);
                    } finally {
                        setTimeout(() => {
                            this.downLoading = false;
                        }, 1500);
                    }
                }
            });
        },
        // 获取tab poptip路径
        getTitlePoptip(item){
            const tabArr = item?.key?.split('|');
            const instanceName = this.instanceList.find(o => o.id === (tabArr?.[0]))?.instanceName || '';
            return instanceName + '/' + tabArr?.[1] + '/' + tabArr?.[3];
        },
        // jsonPath弹窗
        openJsonPathDrawer(){
            this.jsonPathInfo.status = true;
            this.jsonPathInfo.title = this.tabName?.split('|')?.[3] || '';
            this.jsonPathInfo.plugin = this.tabName?.split('|')?.[1] || '';
        },
        // 更新
        async updateJsonPathList(){
            await this.tabClick(this.tabName);
        },
        // 获取tabName对应的jsonPathList配置
        async getJsonPathList(tabName) {
            try {
                const funcName = tabName?.split('|')?.[3] || '';
                const pluginName = tabName?.split('|')?.[1] || '';
                if (!funcName && !pluginName) return [];

                const data = await this.db.get([pluginName, funcName].join('/'), 'management-jsonpath');
                return Array.isArray(data) ? [...data] : [];
            } catch (error) {
                console.warn('获取jsonPath配置失败:', error);
                return [];
            }
        },
        // JSON Pointer 路径存在转义
        unescapeJsonPointer(str) {
            return str.replace(/~1/g, '/').replace(/~0/g, '~');
        },
        // 删除所有jsonPath路径
        deleteAllJsonPaths(obj, jsonPathList) {
            // $.不会被删除  TODO
            try {
                const dataCopy = JSON.parse(JSON.stringify(obj));
                if (!Array.isArray(jsonPathList)) return dataCopy;
                jsonPathList.forEach(item => {
                    const pointers = JSONPath({ path: item.jsonPath, json: dataCopy, resultType: 'pointer' });
                    pointers.forEach(pointer => {

                        // JSON Pointer 路径存在转义，~1 代表 /，~0 代表 ~
                        const keys = pointer.split('/').slice(1).map(str => this.unescapeJsonPointer(str)); // /a/b/0/name
                        if (!keys.length) return;
                        let parent = dataCopy;
                        for (let i = 0; i < keys.length - 1; i++) {
                            if (parent === null || parent === undefined) return;
                            const key = isFinite(keys[i]) ? Number(keys[i]) : keys[i];
                            parent = parent[key];
                        }
                        if (parent === null || parent === undefined) return;
                        const lastKey = isFinite(keys[keys.length - 1]) ? Number(keys[keys.length - 1]) : keys[keys.length - 1];
                        if (Array.isArray(parent) && typeof lastKey === 'number') {
                            // 只删除对应下标的元素
                            parent.splice(lastKey, 1);
                        } else if (typeof parent === 'object' && parent.hasOwnProperty(lastKey)) {
                            // 只删除存在的属性
                            delete parent[lastKey];
                        }
                    });
                });
                return dataCopy;
            } catch (error) {
                return obj; // 返回原始数据作为后备
            }
        },
        // 处理返回数据时加载jsonPath配置
        async processResponseWithJsonPath(data, timeInfo = {}) {
            this.headers = { ...data.response.headers };
            this.totalTime = (timeInfo.totalTime || '-') + 'ms';
            this.apmTime = (timeInfo.apmTime || '-') + 'ms';
            this.manageDurationTime = (timeInfo.duration || '-') + 'ms';
            const { body } = data.response;
            this.jsonData[this.tabName] = JSON.parse(body) || '';
            this.mainTableData[this.tabName] = JSON.parse(body) || '';

            if (!this.jsonData?.[this.tabName] || !Object.keys(this.jsonData?.[this.tabName]).length) {
                this.$hMessage.warning('管理功能无返回数据！');
                this.jsonData[this.tabName] = '';
                this.mainTableData[this.tabName] = '';
                return;
            }

            await this.handleChangeTabShowType(this.tabShowType);
        }
    },
    components: { aButton, apmDropMenuSelect, aTitle, managementBox, jsonViewer, noData, aLoading, menuLayout, codemirror, exportDataModal, batchExportModal, configDataDrawer, jsonPathDrawer, JsonPathTable }
};
</script>
<style lang="less">
.h-select-dropdown.management-query-dropdown-box {
    .h-dropdown-item {
        color: var(--font-color);
    }

    .h-dropdown-item-disabled {
        cursor: not-allowed;
    }

    .h-dropdown-item-disabled:hover {
        background: #262d43;
    }

    .h-dropdown-menu {
        padding: 0;
        background: #262d43;
        border: 1px solid #485565;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    .h-dropdown-item:hover {
        background: #1f3759;
    }

    .dropdown-item-text {
        display: inline-block;
        margin-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");
@import url("@/assets/css/menu.less");
@import url("@/assets/css/json-view");

.main {
    height: 100%;
    overflow: hidden;

    p {
        display: inline-block;
        color: var(--font-color);
        font-size: var(--title-font-size);
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }

    .select-box {
        margin: 0 10px;
    }

    .title {
        min-width: 900px;

        .split-solid {
            float: right;
            width: 1px;
            background-color: #474e6f;
            height: 26px;
            margin-left: 2px;
            margin-right: 5px;
            margin-top: 8px;
        }

        .title-btn {
            float: right;
            margin-right: 5px;
            margin-top: -1px;
        }

        .title-select {
            float: right;
            margin-right: 4px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }

        .icon-setting {
            float: right;
            margin-right: 5px;
            margin-top: 5px;
            cursor: pointer;
            border: var(--border);
            border-radius: 4px;
            height: 32px;
            line-height: 31px;
            padding: 0 5px;
        }
    }

    .apm-box {
        background: transparent;
    }

    .apm-box .left-box .menu > .h-input-wrapper {
        height: 43px;
    }

    .apm-box .left-box .menu .menu-footer {
        bottom: 10px;
    }

    /deep/ .right-box {
        // background: #262d4361;
    }

    .app-select {
        /deep/ .h-select-dropdown {
            top: unset !important;
            left: unset !important;
        }
    }

    .box {
        width: 100%;
        height: 100%;
        display: flex;
        position: relative;
        flex-direction: column;
        flex-grow: 1;
        overflow: hidden;

        .result-box {
            height: 100%;
            position: relative;
            margin-top: 10px;

            .tab-box {
                position: absolute;
                top: 0;
                left: 70%;
            }

            /deep/ .h-tabs-tab {
                padding: 5px;
            }

            /deep/ .h-tabs-nav-container {
                width: calc(100% - 370px);
            }

            .h-tabs {
                height: 44px;
            }

            .tab-body {
                position: relative;
                height: calc(100% - 50px);
            }

            .json-box {
                overflow: hidden;

                /deep/ .jv-container {
                    height: 100%;
                }

                .json-str-viewer {
                    height: 100%;
                    padding: 0 10px;

                    /deep/ .CodeMirror {
                        height: 100%;
                    }
                }
            }

            .table {
                width: 100%;
                padding: 0 5px;
                box-sizing: border-box;
                overflow: auto;
                cursor: pointer;
            }

            .headers {
                position: absolute;
                right: -5px;
                top: -2px;
                margin-right: 15px;
                height: 32px;
                line-height: 30px;
                color: var(--font-color);
                font-size: 14px;
                z-index: 1;
            }
        }
    }

    /deep/ .h-radio-group-item {
        color: #9296a1;
        background: var(--main-color);
    }

    /deep/ .h-radio-wrapper {
        padding: 0 10px;
    }

    /deep/ .h-radio-group-button .h-radio-wrapper-checked {
        border-color: #485565;
        background: #383f59;
        border-radius: 2px;
        color: var(--font-color);
        box-shadow: -1px 0 0 0 #485565;
    }

    /deep/.h-radio-group-button .h-radio-wrapper-disabled:first-child,
    /deep/ .h-radio-group-button .h-radio-wrapper-disabled:hover {
        border-color: #485565;
    }

    /deep/ .h-menu.h-menu-dark.h-menu-vertical {
        height: calc(100% - 130px) !important;
        border-bottom: 1px solid var(--border-color);
    }

    /deep/.h-menu-dark.h-menu-vertical .h-menu-submenu .h-menu-cn.h-menu-item-selected,
    /deep/.h-menu-dark.h-menu-vertical .h-menu-submenu .h-menu-cn:hover {
        &::after {
            position: absolute;
            top: 0;
            left: 0;
            content: "";
            width: 4px;
            height: 40px;
            background: var(--link-color);
        }
    }

    .apm-box .left-box .menu .h-menu-item {
        padding-left: 10px;
        color: var(--font-color);
    }

    .h-menu-cn.h-menu-item {
        height: 40px;
        line-height: 20px;
    }
}

.custom-dropdown-menu {
    padding: 0;
    background: #262d43;
    border: 1px solid #485565;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    min-width: 180px;

    .dropdown-scroll-list {
        max-height: 200px;
        overflow-y: auto;
    }

    .dropdown-close-btn {
        height: 34px;
        line-height: 30px;
        text-align: right;
        border-top: 1px solid #485565;
        background: #262d43;
        position: sticky;
        bottom: 0;
        z-index: 2;

        .h-btn-text {
            color: var(--link-color);
        }
    }
}

.text {
    width: 200px;
    display: inline-block;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
