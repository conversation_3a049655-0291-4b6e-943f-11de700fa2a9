<template>
    <div class="business-box-panel">
        <div class="business-box-title" :title="panel.name || '-'">
            <span class="business-box-title-text" :style="titleStyle">{{ panel.name }}</span>
            <span v-if="panel.sign" ref="titleSign" class='business-box-title-sign'>{{ panel.sign }}</span>
        </div>
        <div v-if="panel.content.length" class="business-box-content-collection">
            <waterfall-layout :id="id" :key="id" :items="panel.content" itemKey="id" :gap="4" :minWidth="minWidth"
                :isEqualHeight="true">
                <template #item="{ item }">
                    <business-box-panel-content :item="item" :mode="mode" :type="type" />
                </template>
            </waterfall-layout>
        </div>
    </div>
</template>

<script>
import WaterfallLayout from '@/components/common/draggableWaterfall/waterfallLayout.vue';
import BusinessBoxPanelContent from './businessBoxPanelContent.vue';

/**
 * 服务面板布局
 */
export default {
    name: 'BusinessBoxPanel',
    components: { WaterfallLayout, BusinessBoxPanelContent },
    props: {
        id: {
            type: String,
            required: true
        },
        panel: {
            type: Object,
            required: true
        },
        mode: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            titleStyle: {}    // 标题文字样式
        };
    },
    computed: {
        minWidth() {
            if(this.mode === 'deploy') {
                return 240;
            }
            return 120;
        }
    },
    watch: {
        'panel.sign'() {
            this.updateTitleTextStyle();
        }
    },
    mounted() {
        this.updateTitleTextStyle();
    },
    methods: {
        updateTitleTextStyle() {
            this.$nextTick(() => {
                if (this.panel.sign) {
                    const offsetWidth = this.$refs.titleSign.offsetWidth;
                    this.titleStyle = {
                        'max-width': `calc(100% - ${offsetWidth * 2 + 4}px)`   // margin-left: 4px
                    };
                    return;
                }
                this.titleStyle = {};
            });
        }
    }
};
</script>

<style lang='less' scoped>
.business-box-panel {
    cursor: default;
    user-select: none;
}

.business-box-title {
    position: relative;
    padding: 0 10px;
    text-align: center;
    line-height: 29px;
    height: 29px;
    background-image: linear-gradient(90deg, #202637 3%, #2F3959 49%, #202637 96%);

    .business-box-title-text {
        display: inline-block;
        max-width: 100%;
        color: var(--font-color);
        font-size: 14px;
        font-weight: 400;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .business-box-title-sign {
        display: inline-block;
        position: absolute;
        top: 50%;
        transform: translate(0, -50%);
        margin-left: 4px;
        padding: 0 2px 1px;
        line-height: 12px;
        color: #28314a;
        font-weight: 500;
        font-size: 10px;
        font-family: PingFangSC-Medium;
        background: rgba(255, 255, 255, 0.55);
        border-radius: 2px;
    }
}

.business-box-content-collection {
    cursor: pointer;
    margin-top: 6px;
}
</style>
