<template>
    <div class="main">
        <a-loading v-if="loading" style="z-index: 9;"></a-loading>

        <!-- 头部 -->
        <div class="title">
            <a-title
                :title="pageName === 'detail' ? $t('pages.dataSecondAppearance.dataSecondAppearance') : ''"
                :class="pageName === 'create' ? 'apm-title-create' : 'apm-title'">
                <template v-slot>
                    <div
                        v-show="pageName === 'create'"
                        class="create-title">
                        <h-icon
                            name="arrow-left-c"
                            @on-click="handlePageJump('detail')">
                        </h-icon>
                        <span>{{ $t('pages.dataSecondAppearance.createSecondAppearanceTask') }}</span>
                    </div>
                    <div
                        v-show="productList.length > 1"
                        class="product-select">
                        <h-select
                            v-model="productInstNo"
                            placement="bottom"
                            :disabled="pageName === 'create'"
                            :positionFixed="true"
                            :clearable="false"
                            @on-change="checkProduct">
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo">
                                {{ item.productName }}
                            </h-option>
                        </h-select>
                    </div>
                </template>
            </a-title>
        </div>

        <!-- 数据二次上场内容 -->
        <second-appearance-detail
            v-if="pageName === 'detail'"
            ref="detail"
            :productId="productInstNo"
            @page-jump="handlePageJump"
        >
        </second-appearance-detail>

        <!-- 创建二次上场任务 -->
        <create-appearance-task
            v-if="pageName === 'create'"
            ref="create"
            :productId="productInstNo"
            @page-jump="handlePageJump"
        >
        </create-appearance-task>

    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import createAppearanceTask from '@/components/dataSecondAppearance/createAppearanceTask/index.vue';
import secondAppearanceDetail from '@/components/dataSecondAppearance/secondAppearanceDetail/index.vue';

export default {
    name: 'DataSecondAppearance',
    components: {
        aTitle,
        aLoading,
        createAppearanceTask,
        secondAppearanceDetail
    },
    data() {
        return {
            productInstNo: '', // 选中的产品
            pageName: 'detail', // tab默认选择
            loading: false
        };
    },
    async mounted() {
        await this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList();
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 切换产品
        async checkProduct(item) {
            this.loading = true;
            localStorage.setItem('productInstNo', item);
            if (this.pageName === 'detail' && this.$refs['detail']) {
                await this.$refs['detail'].initData();
            }
            this.loading = false;
        },
        // 切换页面：创建页 | 列表详情页
        handlePageJump(pageName) {
            this.pageName = pageName;
            this.$nextTick(() => {
                this.$refs[pageName] && this.$refs[pageName].initData();
            });
        }
    },
    beforeDestroy() {
        this.$hCore.off('LoadDataStart');
        this.timer && clearInterval(this.timer);
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.main {
    .title {
        min-width: 1000px;

        .product-select {
            float: right;
            margin-right: 15px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }

        .apm-title-create {
            &::before {
                display: none;
            }
        }
    }

    .create-title {
        float: left;

        .h-icon {
            cursor: pointer;
            margin-right: 8px;

            &:hover {
                color: var(--link-color);
            }
        }
    }
}
</style>
