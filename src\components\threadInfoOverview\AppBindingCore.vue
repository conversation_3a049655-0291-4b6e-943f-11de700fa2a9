<template>
    <div style="height: 100%;">
      <normal-table
        ref="table"
        :formCols="4"
        :formTitle="$t('pages.threadInfoOverview.filterCondition')"
        :tableTitle="$t('pages.threadInfoOverview.threadInfo')"
        :formItems="formItems"
        :columns="threadColumns"
        :loading="tableLoading"
        isSimpleTable
        :total="threadTableTotalCount"
        :tableData="threadTableData"
        :hasPage="true"
        showTitle
        :hasSetTableColumns="false"
        @query="handleQuery"
      >
      </normal-table>
    </div>
  </template>
<script>
import { mapState, mapActions } from 'vuex';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import { getProductInstances } from '@/api/productApi';
import { getThreadInfoCpuNo } from '@/api/threadInfoApi';
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: { normalTable },
    data() {
        return {
            instanceList: [],
            tableLoading: false,
            // 线程信息
            formItems: [
                {
                    type: 'select',
                    label: this.$t('pages.threadInfoOverview.manageIps'),
                    key: 'manageIps',
                    placeholder: this.$t('pages.threadInfoOverview.selectManagementIP'),
                    options: [],
                    value: [],
                    multiple: true
                },
                {
                    type: 'select',
                    label: this.$t('pages.threadInfoOverview.appInstanceNames'),
                    key: 'appInstanceNames',
                    options: [],
                    value: [],
                    placeholder: this.$t('pages.threadInfoOverview.selectApplicationNode'),
                    multiple: true
                },
                {
                    type: 'input',
                    label: this.$t('pages.threadInfoOverview.threadOwner'),
                    key: 'threadOwner',
                    placeholder: this.$t('pages.threadInfoOverview.threadOwnerInputMessage'),
                    value: '',
                    clearable: true
                },
                {
                    type: 'select',
                    label: this.$t('pages.threadInfoOverview.cpuAffinityLabel'),
                    key: 'bindingCpuNo',
                    placeholder: this.$t('pages.threadInfoOverview.selectCpuAffinity'),
                    options: [
                        {
                            value: '1',
                            label: this.$t('pages.threadInfoOverview.cpuAffinityTrue')
                        },
                        {
                            value: '0',
                            label: this.$t('pages.threadInfoOverview.cpuAffinityFalse')
                        }
                    ],
                    value: null,
                    clearable: true
                }
            ],
            threadColumns: [
                {
                    title: this.$t('pages.threadInfoOverview.manageIps'),
                    key: 'manageIp',
                    ellipsis: true
                },
                {
                    title: this.$t('pages.threadInfoOverview.appInstanceNames'),
                    key: 'appInstanceName',
                    ellipsis: true
                },
                {
                    title: this.$t('pages.threadInfoOverview.threadOwner'),
                    key: 'threadOwner',
                    ellipsis: true
                },
                {
                    title: this.$t('pages.threadInfoOverview.threadName'),
                    key: 'threadName',
                    ellipsis: true
                },
                {
                    title: this.$t('pages.threadInfoOverview.threadPriority'),
                    key: 'threadPriority',
                    ellipsis: true
                },
                {
                    title: this.$t('pages.threadInfoOverview.threadCpuNo'),
                    key: 'threadCpuNo',
                    ellipsis: true
                }
            ],
            threadTableData: [],
            threadTableTotalCount: 0
        };
    },
    mounted() {
    },
    beforeDestroy() {},
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight;
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        clearData(){
            this.threadTableTotalCount = 0;
            this.threadTableData = [];
        },
        // 初始化
        async initData() {
            this.clearData();
            await this.getInstanceList();
            // 表格重查询数据
            this.$nextTick(() => {
                this.$refs['table'] && this.$refs['table'].$_handleResetPageDataAndQuery();
            });
        },
        // 获取实例节点列表
        async getInstanceList() {
            try {
                const res = await getProductInstances({
                    productId: this.productInstNo
                });
                if (res.code === '200') {
                    const ips = [...new Set((res?.data?.instances || []).map(o => o?.manageProxyIp))]?.filter(v => v);
                    const instanceNames = [...new Set((res?.data?.instances || []).map(o => o?.instanceName))];
                    this.formItems[0].options = (ips || []).map(o => {
                        return {
                            value: o,
                            label: o
                        };
                    });
                    this.formItems[1].options = (instanceNames || []).map(o => {
                        return {
                            value: o,
                            label: o
                        };
                    });
                } else {
                    this.formItems[0].options = [];
                    this.formItems[1].options = [];
                }
            } catch (err) {
                this.formItems[0].options = [];
                this.formItems[1].options = [];
            } finally {
                this.loading = false;
            }
        },
        // 查询
        async handleQuery(val) {
            try {
                this.tableLoading = true;
                const param = {
                    productId: this.productInstNo,
                    appInstanceNames: val?.appInstanceNames?.join(',') || '',
                    manageIps: val?.manageIps?.join(',') || '',
                    threadOwner: val?.threadOwner || '',
                    bindingCpuNo: val?.bindingCpuNo === '1'
                        ? true : val?.bindingCpuNo === '0' ? false
                            : '',
                    page: val?.page || 1,
                    pageSize: val?.pageSize || 10
                };
                const res = await getThreadInfoCpuNo(param);
                if (this.productInstNo !== param.productId) return;
                if (res.code === '200') {
                    this.threadTableData = res?.data?.list || [];
                    this.threadTableTotalCount = res?.data?.totalCount || 0;
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
                this.tableLoading = false;
            } catch (err) {
                this.threadTableTotalCount = 0;
                this.threadTableData = [];
            } finally {
                this.tableLoading = false;
            }
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
</style>
