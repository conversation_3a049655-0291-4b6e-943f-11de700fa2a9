export default {
    // index
    pleaseSelectNode: '请选择节点',
    currentNoActiveNode: '当前暂无活跃节点',
    inputNodeNameQuery: '输入节点名查询',
    searchManagementFunction: '搜索管理功能',
    pleaseSelectManagementFunction: '请选择管理功能手动发起请求',
    noManagementFunction: '未查询到管理功能',
    showChineseName: '显示"中文译名"',
    noData: '暂无',
    otherData: '其他数据',
    totalTime: '整体耗时：',
    apmTime: 'APM耗时：',
    applicationNodeTime: '应用节点耗时：',
    batchExportTip: '支持批量导出当前产品下的所有管理功能数据',
    quickExportTip: '点击一键导出所选产品管理所有核心的「GetFuncDetailInfo」管理功能',
    downloadConfirm: '您确定下载所有核心节点GetFuncDetailInfo管理功能数据？',
    saveParamTip: '如需保存当前选中功能号输入参数，请手动触发一次查询请求！',
    noReturnData: '功能号无返回数据！',
    initJsonPathConfigFailed: '初始化默认jsonPath配置失败:',
    getJsonPathConfigFailed: '获取jsonPath配置失败:',

    // appConfigInfoDrawer
    infoItem: '基本信息',
    descriptionItem: '入参说明',
    descriptionItem1: '出参说明',
    descriptionItem2: '案例说明',
    nameItem: '入参名称',
    paramType: '参数类型',
    paramDescription: '参数说明',
    nameItem1: '出参名称',
    nameFunction: '功能名称：',
    remarkFunction: '功能备注：',
    versionItem: '版本号：',
    updateTime: '更新时间：',
    provider: '提供者：',
    descriptionFunction: '功能说明：',
    inputData: '入参案例',
    outputData: '出参案例',

    // batchExportModal  exportDataModal
    functionManageName: '管理功能',
    instanceName: '节点',
    pluginName: '插件',
    exportItem1: '正在导出',
    exportItem2: '导出停止',
    exportSuccess: '导出成功',
    exportFailed: '导出失败',
    stopSuccuss: '终止成功',
    stopFailed: '终止失败',
    exportAbnormalItem: '导出终止异常',
    exportSuccessMessage: '导出任务启动成功，请稍候',
    exportFailedLabel: '导出任务启动失败',
    exportListItem: '待导出列表',
    reason: '原因',
    clearList: '清空列表',
    selectManageErrorMessage: '请选择管理功能后查询!',
    webSocketDisconnectionError: '导出进度查询异常，请联系管理员',
    exportFunctionManage: '管理功能导出',
    exportSelectFunctionManageMessage: '根据需要，选择想要导出的管理功能。',
    params: '入参',

    // configDataDrawer
    table: '表格',
    configFunction: '功能配置',
    countMessage: '最大展示Tab个数',
    configItem: '交互配置',
    expandListFunctionMessage: '只保留激活插件功能列表展开',
    recordParamTimesMessage: '记录最后一次输入参数',
    pageFunctionManageMessage: '结果页与管理功能联动',
    typeFunctionMenuDescription: '单击Get类型功能菜单时自动发起请求',

    // jsonPathDrawer
    tableConfigCountFunctionManageDescription: '通过配置JsonPath，将管理功能表格展示的一个结果集拆分为多个表格呈现（一个JsonPath表示拆分一个表格）。',
    pleaseinput: '请输入jsonPath',
    jsonPathMessage: `表格结果路径。核心规则：
            1. 根节点：所有JSONPath都以 $ 开头，表示JSON根对象。
            2. 对象子节点：用 . 连接，如 $.a 表示根下的 a 属性。
            3. 数组元素：用 [] 指定索引（如 $.list[0] ）或通配符 *（如 $.list[*] 表示所有元素）。
            4. 通配符： * 匹配所有同级节点（如 $.a.* 匹配 a 的所有子节点）。
            5. 递归下降： .. 用于匹配任意层级的节点（如 $..name 匹配所有层级的 name 属性）。
            `,
    aliasName: '别名',
    tableNameItem: '表格展示名称',
    message: '是否支持排序',
    tableRowFieldDescription: '表格字段支持行排序(仅当表格为一维数组时生效)',

    // JsonPathTable
    value: '值',
    dataMessage: '处理数据时出错:',

    // managementBox
    descriptionFunctionManageItem: '管理功能使用说明'
};
