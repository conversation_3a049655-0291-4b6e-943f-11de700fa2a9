<template>
  <div class="json-path-table">
    <div v-if="mainTitle" class="title" >{{ mainTitle }}</div>
    <a-table
      v-if="showATable"
      :columns="columns"
      :tableData="tableData"
      :hasPage="false"
      border
      canDrag
      :height='tableData.length ? tableHeight : 42'
      style="margin-bottom: 15px;"
    />
    <div v-else class="html-table" :style="!hasQlobal ? { maxHeight: '550px', overflow: 'scroll' } : {}" v-html="htmlContent"></div>
  </div>
</template>

<script>
import { JSONPath } from 'jsonpath-plus';
import { generateHTMLTable } from 'json5-to-table';
import aTable from '@/components/common/table/aTable';
import { getSpanWidth } from '@/utils/utils';

export default {
    name: 'JsonPathTable',
    components: { aTable },
    props: {
        hasQlobal: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        jsonData: {
            type: Object,
            default: () => ({})
        },
        jsonPath: {
            type: String,
            default: ''
        },
        headerAlias: {
            type: Array,
            default: () => ([])
        },
        sortable: {
            type: Boolean,
            default: false
        },
        tableHeight: { // a-table高度
            type: Number,
            default: 550
        }
    },
    data() {
        return {
            showATable: false,
            columns: [],
            tableData: [],
            htmlContent: '',
            mainTitle: this.title || ''
        };
    },
    watch: {
        jsonData: {
            handler: 'processData',
            deep: true,
            immediate: true
        },
        headerAlias: {
            handler: 'processData',
            deep: true
        }
    },
    methods: {
        findParamDescByParamName(data, key) {
            for (const item of data) {
                if (item?.paramName === key) {
                    return item?.paramDesc;
                }
                if (item?.children) {
                    const result = this.findParamDescByParamName(item?.children || [], key); // 递归处理子节点
                    if (result) {
                        return result;
                    }
                }
            }
            return '';
        },
        // 表头title
        getHeaderAlias(key){
            const alias = this.findParamDescByParamName(this.headerAlias, key) || key;
            return alias;
        },
        // 判断数组类型：基础类型、扁平对象、其他
        getArrayType(arr) {
            if (!Array.isArray(arr) || arr.length === 0) return 'other';
            // 判断是否为基础类型数组
            if (arr.every(v => ['string', 'number', 'boolean', 'undefined'].includes(typeof v) || v === null)) {
                return 'primitive';
            }
            // 判断是否为扁平对象数组
            if (arr.every(item => typeof item === 'object' && item !== null && !Array.isArray(item) &&
                Object.values(item).every(v => ['string', 'number', 'boolean', 'undefined'].includes(typeof v) || v === null))) {
                return 'flatObject';
            }
            // 其它复杂类型
            return 'other';
        },
        // 将复杂数据转换为a-table可展示的格式
        convertToATableFormat(data) {
            try {
                const arrType = this.getArrayType(data);
                if (arrType === 'primitive') {
                    // 基础类型数组
                    return {
                        tableData: data.map((item, index) => ({
                            value: item,
                            __rowKey: index
                        })),
                        columns: this.genATableColumns(data)
                    };
                } else if (Array.isArray(data)) {
                    // 空数组
                    if (data.length === 0) {
                        return {
                            tableData: [],
                            columns: []
                        };
                    }
                    // 兼容数组、对象中混入基础类型，全部包装成对象
                    const tableData = data.map((item, index) => {
                        if (['string', 'number', 'boolean', 'undefined'].includes(typeof item) || item === null) {
                            return { 值: item, __rowKey: index };
                        } else {
                            return { ...item, __rowKey: index };
                        }
                    });
                    // 对象数组
                    return {
                        tableData: tableData,
                        columns: this.genATableColumns(tableData)
                    };
                } else if (typeof data === 'object' && data !== null) {
                    // 单个对象
                    const keys = Object.keys(data);
                    if (keys.length === 0) {
                        // 空对象
                        return {
                            tableData: [],
                            columns: []
                        };
                    }
                    // 复杂对象
                    return {
                        tableData: [{ ...data, __rowKey: 0 }],
                        columns: this.genATableColumns([data])
                    };
                } else {
                    // 基础类型
                    return {
                        tableData: [{ value: String(data), __rowKey: 0 }],
                        columns: this.genATableColumns([data])
                    };
                }
            } catch (error) {
                // 转换失败返回null
                return null;
            }
        },
        // 从数据生成列配置
        genATableColumns(arr) {
            if (!Array.isArray(arr) || arr.length === 0) {
                return [];
            }
            // 基础类型数组
            if (arr.every(v => ['string', 'number', 'boolean', 'undefined'].includes(typeof v) || v === null)) {
                return [{
                    title: '值',
                    key: 'value',
                    sortable: this.sortable,
                    ellipsis: true,
                    minWidth: 120,
                    render: (h, params) => h('span', { attrs: { title: params.row.value } }, params.row.value)
                }];
            }
            // 收集所有可能的键，确保包含所有行的键
            const allKeys = new Set();
            arr.forEach(item => {
                if (typeof item === 'object' && item !== null) {
                    Object.keys(item).forEach(key => {
                        if (key !== '__rowKey') {
                            allKeys.add(key);
                        }
                    });
                }
            });
            // 生成列配置
            return Array.from(allKeys).map(key => ({
                title: key, // 支持表头别名
                key,
                sortable: this.sortable,
                // ellipsis: true,
                minWidth: Math.max(getSpanWidth(key) + 100, 120), // 保证最大宽度
                renderHeader: (h, { column }) => {
                    const displayTitle = this.getHeaderAlias(key);
                    return h('span', { attrs: { title: displayTitle } }, key);
                },
                render: (h, params) => {
                    const value = params.row[key];
                    // 空数组
                    if (Array.isArray(value) && value.length === 0) {
                        return h('span', { attrs: { title: '[]' } }, '[]');
                    }
                    // 空对象
                    if (typeof value === 'object' && value !== null && !Array.isArray(value) && Object.keys(value).length === 0) {
                        return h('span', { attrs: { title: '{}' } }, '{}');
                    }
                    // 复杂对象类型，显示简化的类型标识
                    if (typeof value === 'object') {
                        let displayText = '';
                        let titleText = '';
                        if (Array.isArray(value)) {
                            displayText = `Array[${value.length}]`;
                            titleText = JSON.stringify(value);
                        } else {
                            const keys = Object.keys(value);
                            displayText = `Object{${keys.length}}`;
                            titleText = JSON.stringify(value);
                        }
                        return h('span', {
                            style: { cursor: 'pointer', fontStyle: 'italic' },
                            attrs: { title: titleText }
                        }, displayText);
                    }
                    // 基本类型
                    return h('span', { attrs: { title: value } }, value);
                },
                // 添加排序函数
                sort: (a, b) => {
                    const aVal = a[key];
                    const bVal = b[key];
                    if (typeof aVal === 'number' && typeof bVal === 'number') {
                        return aVal - bVal;
                    }
                    return String(aVal).localeCompare(String(bVal));
                }
            }));
        },
        // 工具方法：递归格式化表格数据，解码unicode字符串
        formatTableValue(data) {
            function decodeUnicode(str) {
                if (typeof str !== 'string') return str;
                return str.replace(/\\u([0-9a-fA-F]{4})/g, (match, grp) => {
                    return String.fromCharCode(parseInt(grp, 16));
                }).replace(/\u([0-9a-fA-F]{4})/g, (match, grp) => {
                    return String.fromCharCode(parseInt(grp, 16));
                });
            }
            if (Array.isArray(data)) {
                if (data.length === 0) return '[]';
                return data.map(item => this.formatTableValue(item));
            } else if (typeof data === 'object' && data !== null) {
                if (Object.keys(data).length === 0) return '{}';
                const result = {};
                Object.keys(data).forEach(key => {
                    result[key] = this.formatTableValue(data[key]);
                });
                return result;
            } else if (typeof data === 'string') {
                return decodeUnicode(data);
            } else {
                return data;
            }
        },
        // 渲染表格为HTML字符串
        renderTable(value) {
            // 基础类型数组纵向展示，带表头
            if (Array.isArray(value) && value.every(v => ['string', 'number', 'boolean', 'undefined'].includes(typeof v) || v === null)) {
                if (value.length === 0) {
                    // 空数组
                    return `<div style="padding: 10px; color: #cacfd4; text-align: center; border: 1px solid #2c334a;">暂无数据</div>`;
                }
                // 字符串数组纵向展示，带表头
                return `
                    <table>
                        <thead>
                            <tr>
                                <th style="color: #fff; background: #2c334a;">值</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${value.map(v => `
                                <tr>
                                    <td title="${v}" style="max-width:350px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap; color: #fff">${v}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            } else if (typeof value === 'object' && value !== null) {
                // 普通对象或对象数组
                const html = generateHTMLTable(this.formatTableValue(value));
                const temp = document.createElement('div');
                temp.innerHTML = html;
                // 设置表头title为别名
                temp.querySelectorAll('th').forEach(th => {
                    const text = th.textContent.trim();
                    th.setAttribute('title', this.getHeaderAlias(text));
                });
                // 设置单元格样式和title
                temp.querySelectorAll('td').forEach(td => {
                    td.style.maxWidth = '350px';
                    // td.style.overflow = 'hidden';
                    // td.style.textOverflow = 'ellipsis';
                    // td.style.whiteSpace = 'nowrap';
                    td.setAttribute('title', td.textContent.trim());
                    // 如果内容为被拆分后的 [] 或 {}，用 span 包裹传值''
                    const text = td.textContent.trim();
                    if (text === '[' || text === ']' || text === '{' || text === '}') {
                        td.innerHTML = `<span></span>`;
                    }
                });
                return temp.innerHTML;
            } else {
                // 基本类型纵向表格展示，带表头
                return `
                    <table>
                        <thead>
                            <tr>
                                <th style="color: #fff; background: #2c334a;">值</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td title="${value}" style="max-width:350px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap; color: #fff">${value}</td>
                            </tr>
                        </tbody>
                    </table>
                `;
            }
        },
        // 主处理逻辑，决定展示a-table还是html-table
        // eslint-disable-next-line complexity
        processData() {
            try {
                // 安全检查：确保jsonData存在且不为null
                if (!this.jsonData || this.jsonData === null) {
                    this.showATable = false;
                    this.htmlContent = '<div style="padding: 10px; color: #cacfd4; text-align: center; border: 1px solid #2c334a;">暂无数据</div>';
                    return;
                }
                if (!this.jsonPath) {
                    // 处理主表格、剩余表格（直接使用传入的数据）
                    if (typeof this.jsonData === 'object' && !Array.isArray(this.jsonData) && Object.keys(this.jsonData).length === 0) {
                        this.showATable = false;
                        this.htmlContent = '<div style="padding: 10px; color: #cacfd4; text-align: center; border: 1px solid #2c334a;">暂无数据</div>';
                        return;
                    }

                    // 特例1: 检查是否是单Key对象,value为基础类型的对象数组或对象数组
                    if (Object.keys(this.jsonData).length === 1) {
                        const key = Object.keys(this.jsonData)[0];
                        const value = this.jsonData[key];

                        // 情况1：Value为基础类型的对象数组
                        if (Array.isArray(value) && (this.getArrayType(value) === 'flatObject')) {
                            if (this.hasQlobal){
                                this.mainTitle = this.title + ' / $.' + key;
                            }
                            const tableResult = this.convertToATableFormat(value);
                            this.tableData = tableResult.tableData;
                            this.columns = tableResult.columns;
                            this.showATable = true;
                            return;
                        }

                        // 情况2：Value为对象数组，必须有且只有一个数组属性，数组数据只能是基础类型数组或扁平对象数组，数组以外的元素是基础类型的
                        if (Array.isArray(value)) {
                            const allHaveSingleArrayProp = value.every(item => {
                                if (typeof item !== 'object' || item === null) return false;
                                // 找出数组属性 数组不止一个
                                const arrayProps = Object.entries(item).filter(([, val]) => Array.isArray(val));
                                if (arrayProps?.length !== 1) return false;

                                // 获取对象的普通属性（排除数组属性） 除数组以外的元素value是基础类型的
                                const normalProps = Object.fromEntries(
                                    Object.entries(item).filter(([, val]) => !Array.isArray(val))
                                );
                                if (this.getArrayType([normalProps]) !== 'flatObject') return false;

                                // 数组属性必须是基础类型数组或扁平对象数组
                                const arrType = this.getArrayType(arrayProps[0][1]);
                                return arrayProps[0][1].length === 0 || arrType === 'primitive' || arrType === 'flatObject';
                            });

                            if (allHaveSingleArrayProp) {
                                // eslint-disable-next-line max-depth
                                if (this.hasQlobal){
                                    this.mainTitle = this.title + ' / $.' + key;
                                }
                                // 处理数组内容
                                const flatData = value.map(item => {
                                    // 找到数组属性
                                    const [arrayPropName, arrayValue] = Object.entries(item).find(([, val]) => Array.isArray(val));
                                    // 获取对象的普通属性（排除数组属性）
                                    const normalProps = Object.fromEntries(
                                        Object.entries(item).filter(([, val]) => !Array.isArray(val))
                                    );

                                    if (arrayValue.length === 0 || this.getArrayType(arrayValue) === 'primitive') {
                                        // 如果是基础类型数组，返回完整的对象
                                        return [{
                                            ...normalProps,
                                            [arrayPropName]: arrayValue.length === 0 ? '[]' : arrayValue.join(', ')
                                        }];
                                    } else {
                                    // 如果是对象数组，将每个对象与外层普通属性合并
                                        return arrayValue.map(subItem => ({
                                            ...normalProps,
                                            ...(typeof subItem === 'object' ? subItem : { value: subItem })
                                        }));
                                    }
                                }).flat();

                                const tableResult = this.convertToATableFormat(flatData);
                                this.tableData = tableResult.tableData;
                                this.columns = tableResult.columns;
                                this.showATable = true;
                                return;
                            } else {
                                // 情况3：Value是复杂嵌套结构
                                this.showATable = false;
                                this.htmlContent = this.renderTable(this.jsonData);
                                return;
                            }
                        }

                        // 情况4：其他类型
                        this.showATable = false;
                        this.htmlContent = this.renderTable(this.jsonData);
                        return;
                    }

                    // 特例2: 多key的对象，value为基础类型的对象 和 一对扁平化对象数组
                    if (Object.keys(this.jsonData).length > 1) {

                        // 只有一个数组。数组属性必须是基础类型数组或扁平对象数组
                        const arrayProps = Object.entries(this.jsonData).filter(([, val]) => Array.isArray(val));
                        // 获取对象的普通属性（排除数组属性）,除数组以外的元素是基础类型的
                        const normalProps = Object.fromEntries(
                            Object.entries(this.jsonData).filter(([, val]) => !Array.isArray(val))
                        );
                        const arrType = this.getArrayType(arrayProps?.[0]?.[1]);
                        const isArrayPropValid = arrayProps?.[0]?.[1]?.length === 0 || arrType === 'primitive' || arrType === 'flatObject';
                        //  如果是基础类型数组，返回完整的对象
                        //  如果是对象数组，将每个对象与外层普通属性合并
                        if ((arrayProps?.length === 1) && (this.getArrayType([normalProps]) === 'flatObject') && isArrayPropValid) {
                            let flatData = [];
                            // 找到唯一数组属性
                            const [arrayPropName, arrayValue] = Object.entries(this.jsonData).find(([, val]) => Array.isArray(val));

                            if (arrayValue.length === 0 || this.getArrayType(arrayValue) === 'primitive') {
                                // 如果是基础类型数组，返回完整的对象
                                flatData = [{
                                    ...normalProps,
                                    [arrayPropName]: arrayValue.length === 0 ? '[]' : arrayValue.join(', ')
                                }];
                            } else {
                                // 如果是对象数组，将每个对象与外层普通属性合并
                                flatData = arrayValue.map(subItem => ({
                                    ...normalProps,
                                    ...(typeof subItem === 'object' ? subItem : { value: subItem })
                                }));
                            }
                            const tableResult = this.convertToATableFormat(flatData);
                            this.tableData = tableResult.tableData;
                            this.columns = tableResult.columns;
                            this.showATable = true;
                            return;
                        } else {
                            this.showATable = false;
                            this.htmlContent = this.renderTable(this.jsonData);
                            return;
                        }

                    }

                    // 其他情况使用renderTable
                    this.showATable = false;
                    this.htmlContent = this.renderTable(this.jsonData);
                } else {
                    // 处理jsonPath表格
                    const result = JSONPath({ path: this.jsonPath, json: this.jsonData || {} });
                    let value = result;
                    if (Array.isArray(result) && result.length === 1) {
                        value = result[0];
                    }
                    if (this.sortable) {
                        const tableResult = this.convertToATableFormat(value);
                        if (tableResult) {
                            this.tableData = tableResult.tableData;
                            this.columns = tableResult.columns;
                            this.showATable = true;
                            this.htmlContent = '';
                        } else {
                            this.showATable = false;
                            this.htmlContent = this.renderTable(value);
                        }
                        return;
                    } else {
                        this.showATable = false;
                        this.htmlContent = this.renderTable(value);
                    }
                }
            } catch (error) {
                console.error('处理数据时出错:', error);
                this.showATable = false;
                this.htmlContent = `<div style="padding: 10px; color: #cacfd4; text-align: center; border: 1px solid #2c334a;">暂无数据</div>`;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.json-path-table {
    margin-bottom: 10px;

    .title {
        position: relative;
        padding: 10px 10px 10px 25px;
        color: #fff;
        // background: var(--input-bg-color);

        &::before {
            content: "";
            display: inline-block;
            position: absolute;
            left: 10px;
            width: 4px;
            height: 17px;
            background: var(--link-color);
        }
    }

    .a-table {
        /deep/ .h-table {
            margin: 0;
            width: 100%;
        }

        /deep/.h-table th {
            height: 33px;
            font-size: 12px;
        }

        /deep/.h-table td {
            height: 33px;
        }

        /deep/.h-table-tiptext {
            height: 42px;
            color: #cacfd4;
            text-align: center;
            border: 1px solid #2c334a;
        }
    }

    .html-table {
        /deep/ table,
        /deep/ table th,
        /deep/ table td {
            margin-right: 2px;
            padding: 4px 10px;
            word-wrap: break-word;
            word-break: break-all;
            word-break: keep-all;
            text-align: left;
            border-bottom: 1px solid #31364a;
            background: var(--input-bg-color);
        }

        /deep/ table {
            width: auto;
            min-width: calc(100% - 2px);
            min-height: 25px;
            line-height: 25px;
            text-align: center;
            table-layout: fixed;
            border-collapse: collapse;
            color: var(--font-color);
        }

        /deep/ table thead {
            position: sticky;
            top: 0;
        }

        /deep/ table th {
            background-color: var(--primary-color);
            outline: 1px solid #31364a;
        }
    }
}
</style>
