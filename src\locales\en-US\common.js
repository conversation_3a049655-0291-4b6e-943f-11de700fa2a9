export default {
    // 操作按钮
    view: 'View',
    query: 'Query',
    reset: 'Reset',
    config: 'Config',
    create: 'Create',
    export: 'Export',
    batchExport: 'Batch Export',
    quickExport: 'Quick Export',
    download: 'Download',
    delete: 'Delete',
    remove: 'Remove',
    clear: 'Clear',
    edit: 'Edit',
    detail: 'Detail',
    add: 'Add',
    save: 'Save',
    saveConfig: 'Save Config',
    cancel: 'Cancel',
    confirm: 'Confirm',
    submit: 'Submit',
    close: 'Close',
    closeAll: 'Close All',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    refresh: 'Refresh',
    search: 'Search',
    select: 'Select',
    selectAll: 'Select All',
    action: 'Action',
    copy: 'Copy',

    // 状态文本
    success: 'Success',
    failed: 'Failed',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    tip: 'Tip',
    loading: 'Loading',
    running: 'Running',
    stopped: 'Stopped',
    paused: 'Paused',
    finished: 'Finished',
    pending: 'Pending',
    processing: 'Processing',
    stop: 'Stop',
    online: 'Online',
    offline: 'Offline',
    expand: 'Expand',
    collapse: 'Collapse',

    // 通用提示
    noData: 'No Data',
    operationSuccess: 'Operation Successful',
    operationFailed: 'Operation Failed',
    confirmDelete: 'Are you sure you want to delete?',
    deleteSuccess: 'Delete Successful',
    saveSuccess: 'Save Successful',
    copySuccess: 'Copy Successful',
    copyMessage: 'This browser does not support copying',
    filterConditionLabel: 'Filter Conditions',
    configTable: 'Config Table',
    moreItems: 'More Conditions',

    // 时间相关
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    startTime: 'Start Time',
    endTime: 'End Time',
    timeRange: 'Time Range',

    // 表单
    required: 'Required',
    optional: 'Optional',
    placeholder: {
        input: 'Please input',
        select: 'Please select',
        search: 'Please input search content'
    },
    unit: {
        items: 'items'
    },
    // 验证相关
    validation: {
    }
};
