<template>
    <div :key="resourceName" class="business-box-content">
        <div class="business-box-panel-content-title">
            <span v-if="clusterName" :title="clusterName" class="cluster-name">{{ clusterName }}</span>
            <span :title="clusterType" class="cluster-type">{{ clusterType }}</span>
        </div>
        <ul v-if="!!nodes.length" class="business-box-panel-content-detail">
            <li v-for="(node, index) of nodes" :key="`${node.id}-${mode}`" ref="nodes"
                :class="['bussiness-btn', ...getNodeClass(node)]">
                <business-poptip v-if="type === 'instance'" :placement="placements[index]" :node="node" />
                <!-- <data-accord-business-poptip v-else-if="type === 'marketStart'"
                    placement={this.getBussinessPosition(node.id, type)} node={node} this.handleDrawerOpen }
                    @drawser-open={  /> -->
            </li>
        </ul>
    </div>
</template>

<script>
import _ from 'lodash';
import BusinessPoptip from './businessPoptip';
// import DataAccordBusinessPoptip from './dataAccordBusinessPoptip';

export default {
    name: 'BusinessBoxPanelContent',
    components: { BusinessPoptip },
    props: {
        item: {
            type: Object,
            required: true
        },
        mode: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            placements: {}
        };
    },
    computed: {
        resourceName() {
            return _.get(this.item, 'target.resourceName') || '';
        },
        clusterName() {
            return _.get(this.item, 'target.resourceNameLeft');
        },
        clusterType() {
            return _.get(this.item, 'target.resourceNameRight') || '';
        },
        nodes() {
            return _.get(this.item, 'nodes') || [];
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.nodes.forEach((_, index) => {
                this.$set(this.placements, index, this.getPopTipPlacement(index));
            });
        });
    },
    methods: {
        getNodeClass(node) {
            const status = _.get(node, 'target.runningInfo.healthStatus');
            const flagStatusClass = this.getFlagStatusClass(status);
            if (status !== 'stopped') {
                const clusterRole = _.get(node, 'target.runningInfo.clusterRole');
                if (clusterRole === 'ARB_ACTIVE') {
                    return [
                        flagStatusClass.status,
                        flagStatusClass.mainFlag
                    ];
                }
                if (clusterRole === 'ARB_INACTIVE') {
                    return [
                        flagStatusClass.status,
                        flagStatusClass.standFlag
                    ];
                }
            }
            return [flagStatusClass.status];
        },

        getFlagStatusClass(key) {
            switch (key) {
                case 'runing':
                case 'normal':
                    return { status: 'bussiness-success', mainFlag: 'main-flag', standFlag: 'stand-flag' };

                case 'warning':
                    return { status: 'bussiness-warn', mainFlag: 'main-flag', standFlag: 'stand-flag' };

                case 'critical':
                    return { status: 'bussiness-error', mainFlag: 'main-flag', standFlag: 'stand-flag' };

                case 'stopped':
                    return { status: 'bussiness-stop', mainFlag: 'main-flag', standFlag: 'stand-flag' };

                default:
                    return { status: 'bussiness-none', mainFlag: 'main-flag', standFlag: 'stand-flag' };
            }
        },

        /**
         * 建议移除，直接在 poptip 组件中处理
         */
        getPopTipPlacement(index) {
            const el = this.$refs.nodes[index];
            if (el && el.getBoundingClientRect) {
                const rect = el.getBoundingClientRect();
                const top = rect.top || 0;
                const left = rect.left || 0;
                const posx = left >= 420 ? 'left' : 'right';
                const posy = top <= 250 ? '-start' : top >= 550 ? '-end' : '';
                return posx + posy;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.business-box-content {
    padding: 4px 4px 2px 4px;
    color: var(--font-color);
    font-size: var(--font-size-base);
    background-color: #262D43;
}

.business-box-panel-content-title {
    display: flex;
    align-items: center;
    padding: 2.5px 0;
    font-family: PingFangSC-Regular;
    color: #CACFD4;
    font-weight: 400;
}

.business-box-panel-content-title>span {
    line-height: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.cluster-name {
    padding-right: 5px;
    border-right: 1px solid #CACFD4;
}

.cluster-type {
    padding-left: 5px;
}

.business-box-panel-content-detail {
    display: inline-grid;
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    grid-auto-rows: row dense;
    grid-gap: 4px;
    width: 100%;
    margin-top: 4px;
    color: var(--font-color);

    &>.bussiness-btn {
        position: relative;
        text-align: center;
        padding: 0 4px;
        height: 24px;
        line-height: 24px;

        /deep/ .h-poptip-rel {
            max-width: 100%;
            padding: 0 2px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }

    .bussiness-success {
        background-color: #26442d;

        &:hover {
            background-color: #3f6b49;
        }
    }

    .bussiness-warn {
        background-color: #4e3b29;

        &:hover {
            background-color: #755b42;
        }
    }

    .bussiness-error {
        background-color: #4c2132;

        &:hover {
            background-color: #73354d;
        }
    }

    .bussiness-none {
        background-color: #1f3759;

        &:hover {
            background-color: #305180;
        }
    }

    .bussiness-stop {
        background-color: #323952;

        &:hover {
            background-color: #4d5778;
        }
    }

    .main-flag {
        .node-flag("static/mainFlag.png");
    }

    .stand-flag {
        .node-flag("static/standFlag.png");
    }
}

.node-flag(@url) {
    &::after {
        display: inline-block;
        position: absolute;
        left: 1px;
        top: 1px;
        width: 20px;
        height: 20px;
        content: "";
        background: url(@url);
        background-size: 15px 14px;
        background-repeat: no-repeat;
    }
}
</style>
