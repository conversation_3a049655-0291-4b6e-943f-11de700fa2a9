<template>
    <div class="main">
        <!-- 头部 -->
        <table-sql-top
            ref="manage-top"
            class="title-line"
            :isCores="true"
            :disabled="loadRuning"
            @loginSuccess="getMemoryNodeByTables"
            @connect-database="connectDatabase"
            @clear-data="clearData">
        </table-sql-top>
        <a-loading v-if="loading" style="z-index: 101;"></a-loading>
        <!-- 内存表管理 -->
        <h-tabs
            v-if="isShowTable"
            ref="productBox"
            v-model="tabName"
            class="product-box">
            <h-tab-pane label="SQL" name="SQL">
                <sql-edit
                    v-show="sqlEditVisible"
                    ref="sqlEditor"
                    :isChange="true"
                    :tableList="tableTreeList.map(v => v.title)"
                    :style="{height: `${lastEditHeight}px`}"
                    @code-change="onChangehandle" />
                <h-row class="sql-resizer">
                    <div
                        class="y-resizer"
                        @mousedown="mouseDown">
                    </div>
                    <span class="cate-switch"
                        @click="changeShowHidden">
                        <h-icon
                            :name="sqlEditVisible ? 'packup' : 'unfold'"
                            size='14'
                            color="#cacfd4">
                        </h-icon>
                    </span>
                </h-row>
                <sql-result
                    ref="sqlResult"
                    :resultList="resultList"
                    :clusterRoles="clusterRoles"
                    :style="sqlEditVisible ?
                        { height: `calc(100% - ${(editHeight + 20)}px)` } :
                        { height: '100%' }"
                    :height="tableHeight"
                    @callSetLoginInfo="callSetLoginInfo"
                    @handlePageChange="handlePageChange" />
            </h-tab-pane>
            <template v-slot:extra>
                <div style="position: fixed; display: flex; right: 82px; top: 6px;">
                    <a-button
                        v-show="tabName === 'SQL'"
                        type="primary"
                        :loading="loadRuning"
                        style="margin-right: 6px;"
                        @click="verifySqlNodes">执行</a-button>
                    <a-button
                        type="dark"
                        :disabled="loadRuning"
                        @click="handleHistoryDrawer">历史SQL</a-button>
                    <!--分隔符-->
                    <div
                        class="input-line"
                        style="margin: 0 6px;"></div>
                    <h-input
                        v-model.number="timeout"
                        :disabled="loadRuning"
                        type="int"
                        titleShow
                        specialFilter
                        :specialLength="10"
                        :specialDecimal="0"
                        style="width: 115px; margin-right: 6px;">
                        <span
                            slot="prepend"
                            style="background: var(--button-bg-color);">超时:</span>
                        <span slot="append">秒</span>
                    </h-input>
                    <a-button
                        type="dark"
                        icon="help"
                        title="帮助手册"
                        style="padding: 6px 10px;"
                        :disabled="loadRuning"
                        @click="() => {instructInfo.status = true;}"></a-button>
                </div>
            </template>
        </h-tabs>
        <template v-else>
            <no-data text="请选择并连接应用节点！" />
        </template>
        <!-- 导出历史弹窗 -->
        <history-list-modal
            v-if="historyInfo.status"
            :modalInfo="historyInfo"
            :historyList="historyList"
            @addSqlToEdit="addSqlToEdit" />

        <instruct-modal v-if="instructInfo.status" :modalInfo="instructInfo" />
        <!-- 节点表校验 -->
        <node-verify-modal
            v-if="nodeVerifyInfo.status"
            :modalInfo="nodeVerifyInfo"
            :intances="intances"
            :clusterRoles="clusterRoles"
            @verifySubmit="verifySubmit" />

        <!-- SQL二次确认 -->
        <sql-run-verify-modal
            v-if="sqlRunInfo.status"
            :modalInfo="sqlRunInfo"
            @verifySubmit="verifySecondSubmit" />
    </div>
</template>

<script>
import _ from 'lodash';
import { Parser } from 'node-sql-parser';
import { getSpanWidth, getByteSize, tryGetSqlSelectAst, areObjectArraysEqualUnordered } from '@/utils/utils';
import {  getMonitorHeartbeats } from '@/api/httpApi';
import { MDB_NO_LOGIN } from '@/config/errorCode';
import { getMemoryDataBySql, getMemoryNodeByTables } from '@/api/memoryApi';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import tableSqlTop from '@/components/sqlTable/tableSqlTop';
import sqlEdit from '@/components/sqlTable/sqlEdit/sqlEdit';
import sqlResult from '@/components/sqlTable/sqlResultNew/sqlResultNew';
import sqlRunVerifyModal from '@/components/sqlTable/modal/sqlRunVerifyModal';
import nodeVerifyModal from '@/components/sqlTable/modal/nodeVerifyModal';
import instructModal from '@/components/sqlTable/modal/instructModal.vue';
import historyListModal from '@/components/sqlTable/modal/historyListModal.vue';

// 连接数据库
export default {
    name: 'SqlCores',
    components: { aLoading, sqlResult, noData, sqlEdit, aButton, tableSqlTop, historyListModal, instructModal, nodeVerifyModal, sqlRunVerifyModal },
    data() {
        return {
            endpointId: '',
            loading: false,
            loadRuning: false,
            isShowTable: false,
            tableTreeList: [],
            tableList: [],
            resultList: [],
            instanceName: '',
            sqlTempList: [], // sql模板
            tabName: 'SQL',
            newTabName: '',
            intances: [],
            dropMenuList: [],
            tabList: [],
            searchText: '',
            isOpen: true,
            historyList: [],
            historyInfo: {
                status: false
            },
            instructInfo: {
                status: false
            },
            nodeVerifyInfo: {
                hasWriteRedo: true,
                status: false,
                astResult: null,
                suggestList: []
            },
            sqlRunInfo: {
                status: false,
                sql: '',
                coreList: []
            },
            timeout: 10,
            maxTimeWait: 10 * 60,
            sqlEditVisible: true,
            // sqlEdit 拖动相关
            startY: 0,
            editHeight: 200,
            lastEditHeight: 200,
            clusterRoles: {},
            tableNameForInstanceIds: []
        };
    },
    computed: {
        tableHeight: function() {
            if (this.sqlEditVisible) {
                return (this.$refs.productBox?.$el?.clientHeight || 0) - this.editHeight - 190;
            }
            return (this.$refs.productBox?.$el?.clientHeight || 0) - 190;
        }
    },
    async mounted() {
        this.loading = true;
        try {
            const productInstNo = localStorage.getItem('productInstNo') || '';
            await this.$refs['manage-top'].init(productInstNo);
            this.$nextTick(() => {
                this.clusterRoles = this.$refs['manage-top'].clusterRoles;
            });
            this.isFirstRender = true;
        } finally {
            this.loading = false;
        }
        window.addEventListener('resize', this.resize);
    },
    methods: {
        resize() {
            const h = this.editHeight;
            this.editHeight = 0;
            this.$nextTick(() => {
                this.editHeight = h;
            });
        },
        // 清理数据
        clearData() {
            this.tableTreeList = [];
            this.tableList = [];
            this.tabName = 'SQL';
            this.tabList = [];
            this.intances = [];
        },
        // 添加sql查询标签
        handleTabsAdd(idx) {
            this.$hMessage.confirm({
                title: '请输入SQL标签名',
                onOk: () => {
                    if (this.newTabName === '') {
                        this.$hMessage.error('标签名不得为空');
                        return;
                    } else if (this.tabList.indexOf(this.newTabName) > 0) {
                        this.$hMessage.error('当前标签名已存在');
                        return;
                    }
                    this.tabList.push(this.newTabName);
                },
                render: (h) => {
                    return h('Input', {
                        props: {
                            value: this.newTabName,
                            autofocus: true,
                            placeholder: '请输入标签名'
                        },
                        on: {
                            input: val => {
                                this.newTabName = val;
                            }
                        }
                    });
                }
            });
        },
        // 成功获取产品列表
        async productListCompleted() {
            const routeParam = localStorage.getItem('productInstNo');
            if (routeParam) {
                await this.$refs['manage-top'].init(routeParam);
            }
        },
        // 连接内存表
        async connectDatabase(param) {
            this.endpointId = param.endpointId;
            this.intances = param?.instances || [];
            this.isShowTable = Boolean(this.endpointId);
            this.clusterRoles = this.$refs['manage-top'].clusterRoles;
            if (this.isShowTable){
                this.$nextTick(() => {
                    if (sessionStorage.getItem('apm.sqlCoresTableCode')){
                        this.$refs['sqlEditor'].setSqlCode(sessionStorage.getItem('apm.sqlCoresTableCode'));
                    } else {
                        this.$refs['sqlEditor'].setSqlCode('');
                    }
                });
            }
        },
        // 编辑器code change
        onChangehandle(sqlCode){
            const size = getByteSize(sqlCode);
            if (size < 1024 * 1024){
                sessionStorage.setItem('apm.sqlCoresTableCode', sqlCode);
            } else {
                this.$hMessage.info('编辑器内容超过1M不支持暂存!');
            }
        },
        // 过滤树菜单字段
        treeFilterMethod(regVal) {
            const nodeList = this.$refs['tree'].getAllNodesState();
            Array.isArray(nodeList) && nodeList.forEach(item => {
                let hiddenStu = false, filterStu = false;
                if (regVal) {
                    filterStu = item.node.title.search(regVal) !== -1;
                    hiddenStu = !filterStu;
                }
                this.$set(item.node, 'filterable', filterStu);
                this.$set(item.node, 'hidden', hiddenStu);
            });
        },
        // 打开历史记录侧边栏
        handleHistoryDrawer() {
            const history = localStorage.getItem('HISTORY_CORES_SQLS');
            this.historyList = history ? JSON.parse(history) : [];
            this.historyInfo.status = true;
        },
        // 默认SELECT添加limit
        addLimitToSQL(sql) {
            let newSql = '';
            const selectPattern = /\bSELECT\b/i;
            const limitPattern = /\bLIMIT\b/i;
            const list = sql.split(';');
            if (list[0].trim()) {
                if (!selectPattern.test(list[0]) || limitPattern.test(list[0])) {
                    newSql = list[0] + ';';
                } else if (selectPattern.test(list[0])) {
                    newSql = list[0] + ';';
                }
            }
            return newSql;
        },
        /**
         * 打开登录弹窗
         */
        callSetLoginInfo() {
            this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
        },
        // 查询表和实例对应关系
        async getMemoryNodeByTables() {
            this.loadRuning = true;
            try {
                const res = await getMemoryNodeByTables({
                    productId: localStorage.getItem('productInstNo')
                });
                this.tableNameForInstanceIds = res?.data || [];
            } catch (error) {
                console.error(error);
            }
            this.loadRuning = false;
        },
        // 校验sql核心节点
        verifySqlNodes() {
            this.nodeVerifyInfo.astResult = null;
            this.nodeVerifyInfo.suggestList = [];
            const sqlList = this.$refs.sqlEditor.getSqlList();
            // 如果没有，设置默认值校验
            if (!localStorage.getItem('apm.mdbsql.tableCheck')) {
                localStorage.setItem('apm.mdbsql.tableCheck', 'true');
            }
            if (!this.validateInputs(sqlList)) return;
            const sql = sqlList[0];
            const parser = new Parser();
            let astResult;
            try {
                astResult = parser.astify(sql); // 解析 SQL 查询
                const instanceList = this.$refs['manage-top'].instanceList || [];

                if (localStorage.getItem('apm.mdbsql.tableCheck') === 'true') {
                    const astNode = astResult?.[0];
                    const isSelectWithoutInstances = astNode?.type === 'select' && this.intances.length === 0;

                    if (astNode?.type !== 'select' || isSelectWithoutInstances) {
                        const from = astNode?.type === 'select' ? astNode?.from || [] : astNode?.table;

                        if (from.length === 1 && from[0]?.table) {
                            const tableData = _.find(this.tableNameForInstanceIds, ['tableName', from[0].table]);
                            const ids = tableData?.instanceIds || [];
                            const instanceIds = (tableData?.hasWriteRedo && astNode?.type !== 'select')
                                ? _.filter(ids, (o) => {
                                    return _.find(instanceList, ['instanceId', o.instanceId])?.clusterRole === 'ARB_ACTIVE';
                                })
                                : ids;

                            this.nodeVerifyInfo.suggestList = instanceIds.map((obj) => ({
                                label: obj.instanceNo,
                                value: obj.instanceId
                            }));

                            // eslint-disable-next-line max-depth
                            if (!areObjectArraysEqualUnordered(this.nodeVerifyInfo.suggestList, this.intances)) {
                                this.nodeVerifyInfo.astResult = astResult;
                                this.nodeVerifyInfo.hasWriteRedo = tableData?.hasWriteRedo;
                                this.nodeVerifyInfo.instanceList = instanceList;
                                this.nodeVerifyInfo.status = true;
                                return;
                            }
                        }
                    }
                }
            } catch (error) {
                console.error(error);
            }
            this.secondVerifySqlNodes(sqlList, astResult, this.intances);
        },
        // 校验输入格式
        validateInputs(sqlList) {
            if (!this.intances.length && localStorage.getItem('apm.mdbsql.tableCheck') === 'false') {
                this.$hMessage.error('请选择要运行的节点！');
                return false;
            }

            if (!sqlList.length) {
                this.$hMessage.error('SQL输入不能为空！');
                return false;
            }

            if (sqlList.length > 1) {
                this.$hMessage.error('请选择要执行的sql语句，不得超过1条！');
                return false;
            }

            if (!this.timeout) {
                this.$hMessage.error('超时时间不能配置为空或0！');
                return false;
            }

            if (this.timeout > this.maxTimeWait) {
                this.$hMessage.error('超时时间不得超过10分钟！');
                return false;
            }

            return true;
        },
        // 勾选执行节点后返回
        verifySubmit(astResult, list, adviceRadio) {
            const sqlList = this.$refs.sqlEditor.getSqlList();
            this.secondVerifySqlNodes(sqlList, astResult, list);
            if (adviceRadio) {
                this.$refs?.['manage-top']?.$refs?.['apm-drop-menu-select'] &&
                    this.$refs['manage-top'].$refs['apm-drop-menu-select'].setSelectMenu(list);
            }
        },
        // sql二次确认
        secondVerifySqlNodes(sqlList, astResult, list) {
            if (!localStorage.getItem('apm.mdbsql.sqlValid')) {
                localStorage.setItem('apm.mdbsql.sqlValid', 'update');
            }
            if (!list.length) {
                this.$hMessage.error('请选择要运行的节点！');
                return;
            }
            const sqlType = astResult?.[0]?.type;
            if (localStorage.getItem('apm.mdbsql.sqlValid') === 'never' || sqlType === 'select') {
                this.handleSqlRun(sqlList, list);
            } else {
                if (localStorage.getItem('apm.mdbsql.sqlValid') === 'update' &&
                    ['update', 'insert', 'delete'].includes(sqlType)) {
                    const instanceList = this.$refs['manage-top'].instanceList;
                    this.sqlRunInfo.status = true;
                    this.sqlRunInfo.sqlList = sqlList;
                    this.sqlRunInfo.coreList = _.filter(instanceList, (obj1) =>
                        _.some(list, (obj2) => obj1.instanceId === obj2.value)
                    );
                    return;
                }
                this.handleSqlRun(sqlList, list);
            }
        },
        // 二次确认回掉
        verifySecondSubmit(sqlList, list) {
            this.handleSqlRun(sqlList, list);
        },
        // SQL执行
        // eslint-disable-next-line complexity
        async handleSqlRun(sqlList, coreList) {
            this.loadRuning = true;
            const param = {
                productInstNo: localStorage.getItem('productInstNo'),
                performSqlTimeout: this.timeout * 1000,
                endpointId: this.endpointId,
                page: 1,
                pageSize: 10
            };
            this.resultList = [];
            this.$refs['sqlResult'].clearData();
            let index = 0;
            const runList = coreList || this.intances;
            this.insertSqlHistory(sqlList[0], runList);
            while (runList.length > index) {
                const item = sqlList[0];
                param.sql = this.addLimitToSQL(item);
                param.instanceId = runList[index]?.value;
                try {
                    const startTime = Date.now();
                    const res = await getMemoryDataBySql(param, param.performSqlTimeout + 2000); // 比后端时间多加1s，用来获取返回结果
                    let tableRecords, ast = {};
                    if (res.code === MDB_NO_LOGIN){
                        this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
                        break;
                    }
                    this.$refs?.['manage-top'] && this.$refs['manage-top'].updateToken(res?.headers?.authorization);
                    if (res.success) {
                        tableRecords = res.data.tableRecords;
                        try {
                            ast = tryGetSqlSelectAst(param.sql);
                        } catch (error) {
                            console.error(error);
                        }
                    } else {
                        tableRecords = [{
                            error_info: res.message
                        }];
                    }
                    const sqlRunTime = res.tracing && res.tracing.find(item => item.spanName === 'appApmSqlExecuteExitLinkLatency')?.duration;
                    const apmTime = res.tracing && res.tracing.find(item => item.spanName === 'appApmSqlExecuteEnterLinkLatency')?.duration;
                    const totalTime = Date.now() - startTime;
                    const node = {
                        key: index,
                        id: runList[index]?.value, // instaceId 用于显示tabName的主表示同意，不从缓存中取
                        label: runList[index]?.label,
                        columns: [],
                        sql: param.sql,
                        ast,
                        isExport: (ast.type === 'select' && ast?.from?.length === 1),
                        time: res?.data?.time || '-',
                        sqlRunTime: sqlRunTime || '-',
                        apmTime: apmTime || '-',
                        totalTime,
                        data: tableRecords,
                        pageSize: 10,
                        page: 1,
                        totalCount: res?.data?.totalCount,
                        param: { ...param }
                    };
                    if (Array.isArray(Object.keys(tableRecords?.[0] || {}))) {
                        Object.keys(tableRecords?.[0] || {}).forEach(ele => {
                            let width = getSpanWidth(ele);
                            width += 40;
                            node.columns.push({
                                title: ele,
                                key: ele,
                                minWidth: width > 150 ? width : 150,
                                ellipsis: true
                            });
                        });
                        this.resultList.push(node);
                    }
                } catch (error) {
                    console.error(error);
                    break;
                }
                index++;
            }
            this.loadRuning = false;
        },
        // 处理分页
        // eslint-disable-next-line max-params
        async handlePageChange({ index, pageNo, pageSize, onSetPage }) {
            if (this.isFetching) return;
            this.isFetching = true;
            const curTable = this.resultList[index];
            try {
                const startTime = Date.now();
                const param = {
                    productInstNo: localStorage.getItem('productInstNo'),
                    endpointId: this.endpointId,
                    instanceId: curTable.id,
                    page: pageNo,
                    pageSize: pageSize,
                    sql: curTable.sql,
                    performSqlTimeout: this.timeout * 1000
                };
                let tableRecords = [];
                const res = await getMemoryDataBySql(param, param.performSqlTimeout + 2000); // 比后端时间多加1s，用来获取返回结果
                if (res.code === MDB_NO_LOGIN){
                    this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
                    return;
                }
                if (res.success) {
                    tableRecords = res.data?.tableRecords || [];
                    if (tableRecords.length === 0) {
                        this.$hMessage.error('该页数据不存在，请重新执行');
                    }
                } else {
                    tableRecords = [{
                        error_info: res.message
                    }];
                }
                const sqlRunTime = res.tracing && res.tracing.find(item => item.spanName === 'appApmSqlExecuteExitLinkLatency')?.duration;
                const apmTime = res.tracing && res.tracing.find(item => item.spanName === 'appApmSqlExecuteEnterLinkLatency')?.duration;
                const totalTime = Date.now() - startTime;
                this.resultList[index].data = tableRecords;
                this.resultList[index].pageSize = param.pageSize;
                this.resultList[index].page = param.page;
                this.resultList[index].time = res?.data?.time || '-';
                this.resultList[index].sqlRunTime = sqlRunTime || '-';
                this.resultList[index].apmTime = apmTime || '-';
                this.resultList[index].totalTime = totalTime;
                onSetPage(param.page, param.pageSize);
                return tableRecords;
            } finally {
                this.isFetching = false;
            }
        },
        // 将运行的sql插入历史数据
        async insertSqlHistory(sql, runList) {
            const history = localStorage.getItem('HISTORY_CORES_SQLS');
            let list = [];
            if (history) {
                list = JSON.parse(history);
                list.length >= 50 && list.pop();
            }

            list.unshift({
                sql: sql,
                databaseName: runList,
                time: new Date()
            });
            localStorage.setItem('HISTORY_CORES_SQLS', JSON.stringify(list));
        },
        // 将历史sql插入编辑器--覆盖原有sql
        addSqlToEdit(sql, databaseName) {
            this.$refs?.['manage-top']?.$refs?.['apm-drop-menu-select'] && this.$refs['manage-top'].$refs['apm-drop-menu-select'].setSelectMenu(databaseName);
            setTimeout(() => {
                this.$refs['sqlEditor'].setSqlCode(sql);
            }, 0);
            this.historyInfo.status = false;
        },
        changeShowHidden() {
            this.sqlEditVisible = !this.sqlEditVisible;
            this.$nextTick(() => {
                this.resize();
            });
        },
        // 编辑框上下滑动
        mouseDown(e) {
            if (!this.sqlEditVisible) return;
            this.startY = e.clientY;
            this.mouseMove(e);
            this.mouseUp();
            document.addEventListener('mousemove', this.mouseMove);
            document.addEventListener('mouseup', this.mouseUp);
        },
        mouseUp() {
            this.editHeight = this.lastEditHeight;
            document.removeEventListener('mousemove', this.mouseMove);
            document.removeEventListener('mouseup', this.mouseUp);
        },
        mouseMove(e) {
            e.preventDefault();
            e.stopPropagation();
            if (e.clientY < 220 || e.clientY > 356) return;
            const offset = e.clientY - this.startY;
            if (offset) {
                this.lastEditHeight = offset + this.editHeight;
            }
        },
        // 获取心跳数据
        getMonitorHeartbeats() {
            const param = {
                productId: localStorage.getItem('productInstNo'),
                type: 'instance'
            };
            return new Promise((resolve, reject) => {
                getMonitorHeartbeats(param).then(res => {
                    resolve(res?.data || []);
                }).catch(error => {
                    reject(error);
                    console.error(error);
                });
            });
        }
    },
    watch: {
        searchText(val) {
            // 过滤查询条件大小写
            const regVal = val ? new RegExp(`${val}`, 'i') : '';
            this.treeFilterMethod(regVal);
        },
        isShowTable() {
            this.$nextTick(() => {
                this.resize();
            });
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");

.title-line {
    position: relative;
    width: 100%;
    min-width: 700px;
}

.product-box {
    min-width: 900px;

    /deep/ .h-tabs-content-wrap {
        height: calc(100% - 40px);
    }

    /deep/.h-tabs-nav-right {
        position: fixed;
        width: 100%;
    }

    /deep/ .h-tabs-bar {
        margin-bottom: 0;
    }

    /deep/ .h-tabs-return,
    /deep/ .h-tabs-enter {
        padding: 4px 5px 0 0;
    }

    /deep/ .apm-box {
        margin-top: 0;
        height: 100%;
    }

    /deep/ .h-input-group {
        border: 1px solid var(--base-color);
        border-radius: 4px;
    }

    /deep/ .h-input-group > .h-input-group-prepend {
        background-color: var(--button-bg-color);
        color: #fff;
        border: none;
    }

    /deep/ .h-input-group > .h-input-group-append {
        background-color: var(--button-bg-color);
        color: #fff;
        border: none;
        border-left: 1px solid var(--base-color);
    }

    /deep/ .h-input-group > .h-input {
        background-color: var(--button-bg-color);
        border: none;
    }

    .input-line {
        display: inline-block;
        width: 1px;
        height: 34px;
        margin: 0 4px;
        text-align: center;
        background: var(--base-color);
    }

    /deep/ .h-table-cell span {
        white-space: pre;
    }
}

.sql-resizer {
    width: 100%;

    .y-resizer {
        position: absolute;
        left: 0;
        right: 0;
        top: -2px;
        height: 1px;
        user-select: none;
        cursor: row-resize;
        background-color: var(--base-color);

        &:hover {
            height: 3px;
            background-color: var(--base-color);
        }
    }

    .cate-switch {
        position: absolute;
        width: 44px;
        height: 12px;
        left: 50%;
        top: 0;
        line-height: 12px;
        text-align: center;
        background: rgba(209, 216, 229, 0.2);
        border-radius: 0;
        z-index: 4;
        cursor: pointer;
        transform: perspective(0.5em) rotateX(-10deg);

        &:hover {
            background: rgba(209, 216, 229, 0.4);
        }
    }
}
</style>
