/**
 * 布局缓存处理
 */
export default class {

    constructor(id, itemKey) {
        this._id = id;
        this.itemKey = itemKey;
    }

    get id() {
        return `waterfall-${this._id}`;
    }

    /**
     * 缓存数据
     */
    cache(layout) {
        try {
            if (
                this.id === undefined ||
                this.itemKey === undefined ||
                layout === undefined
            ) {
                console.warn(this.$t('pages.common.layoutCacheWarning'));
                return;
            }

            const cache = layout.map((col) => col.map((item) => item[this.itemKey]));
            localStorage.setItem(this.id, JSON.stringify(cache));
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * 获取历史布局
     */
    getCache() {
        try {
            const layout = localStorage.getItem(this.id);
            if (layout) {
                return JSON.parse(layout);
            }
            return [];
        } catch (e) {
            console.error(e);
            return [];
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        localStorage.removeItem(this.id);
    }
}
