import './apmBlank.less';
export default {
    name: 'apmBlank',
    props: {
        width: {
            type: String,
            default: '150'
        },
        height: {
            type: String,
            default: '100'
        },
        name: {
            type: String,
            default: 'Data' // Data, Service, Config
        }
    },
    data() {
        return {};
    },
    render() {
        return <div class="no-data">
            <img
                width={this.width}
                height={this.height}
                src={`${this.IMG_HOME}static/no${this.name}.png` }
                alt={this.$t('pages.common.imageNotDisplayed')} />
            {this.$slots.default}
        </div>;
    }
};
