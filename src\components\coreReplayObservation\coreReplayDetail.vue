<template>
    <div class="core-replay-detail-box">
      <a-loading v-if="loading"></a-loading>
      <!-- 头部 -->
      <div class="title">
        <a-title class="apm-title-create">
          <template v-slot>
            <div class="slot-container">
              <div class="create-title">
                <h-icon name="arrow-left-c" @on-click="handleCreateCancel">
                </h-icon>
                <span>具体任务细节</span>
                <span class="split-solid"></span>
                <span style="font-size: 12px;"
                  ><span :class="`status status-${status || ''}`"></span
                  >{{ statuslabel }} ({{taskNum}})</span
                >
                <span class="split-solid" style="height: 12px;"></span>
                <span style="font-size: 12px;"
                  >开始时间：{{ startTime || "-" }}</span
                >
              </div>
              <div class="product-select">
                <span style="font-size: 12px;">
                  <template>
                    <h-icon size="14" name="refresh"></h-icon>
                    <template v-if="isLoadTab">正在加载</template>
                    <template v-else-if="isRefreshing">数据更新中</template>
                    <template v-else> {{ autoUpdateTime }}s 数据更新 </template>
                  </template>
                </span>
                <span class="split-solid"></span>
                <!-- <a-button
                  v-show="hasStatusBtn('start')"
                  type="dark"
                  style="margin-right: 5px;"
                  @click="onButtonClick('start')"
                  >启动
                </a-button>
                <a-button
                  v-show="hasStatusBtn('resume')"
                  type="dark"
                  style="margin-right: 5px;"
                  @click="onButtonClick('resume')"
                  >恢复
                </a-button>
                <a-button
                  v-show="hasStatusBtn('paused')"
                  type="dark"
                  style="margin-right: 5px;"
                  @click="onButtonClick('paused')"
                  >暂停
                </a-button>
                <a-button
                  v-show="hasStatusBtn('terminated')"
                  type="danger"
                  style="margin-right: 5px;"
                  @click="onButtonClick('terminated')"
                  >终止
                </a-button> -->
                <h-select v-model="productInstNo" disabled>
                  <h-option
                    v-for="item in productList"
                    :key="item.id"
                    :value="item.productInstNo"
                  >
                    {{ item.productName }}
                  </h-option>
                </h-select>
              </div>
            </div>
          </template>
        </a-title>
      </div>
      <div class="product-box">
        <h-tabs
          ref="tabs"
          v-model="tabName"
          class="tab-box"
          :showArrow="false"
          @on-click="handleTabChange(tabName)"
        >
          <h-tab-pane
            v-for="item in taskList"
            :key="item.tradeDate"
            :label="label(item)"
            :name="item.tradeDate"
          >
          </h-tab-pane>
        </h-tabs>
        <div v-if="dateStatus && dateStatus !== 'not_started'"  class="content-box" >
          <a-tips
            style="margin: 10px 0 0;"
            theme="dark"
            :tipText="`重演日期：${tabName || '-'}；  重演交易日文件目录：${replayFilePath || '-'}`"
          ></a-tips>
          <div class="info-bar-box">
            <info-sum-bar :data="resultInfo" class="info-bar-box-1"
              :selectInfoId="selectInfoId"
              selectedStyleType="border"
              @info-bar-click="handleBarClick"
            >
              <template v-slot:extraTitleBox>
                <h-poptip
                  autoPlacement
                  trigger="hover"
                  customTransferClassName="apm-poptip monitor-poptip"
                >
                  <h-icon
                    name="ios-information-outl"
                    class="poptip-icon"
                  ></h-icon>
                  <div slot="content" class="pop-content" style="white-space: normal;">
                    当前交易日重演完成时，将展示此交易日中"重演"与"持久化"中相同/不同表数量、应答的一致/不一致数量
                  </div>
                </h-poptip>
              </template>
            </info-sum-bar>
            <info-sum-bar :data="sereInfo" class="info-bar-box-2">
              <template v-slot:extraTitleBox>
                <h-poptip
                  autoPlacement
                  trigger="hover"
                  customTransferClassName="apm-poptip monitor-poptip"
                >
                  <h-icon
                    name="ios-information-outl"
                    class="poptip-icon"
                  ></h-icon>
                  <div slot="content" class="pop-content" style="white-space: normal;">
                    当前交易日消息收发的实时状态。应答比对：当前交易日"重演的应答" 与 "生产已记录的应答"比对。
                  </div>
                </h-poptip>
              </template>
            </info-sum-bar>
            <info-sum-bar :data="supInfo" class="info-bar-box-3">
              <template v-slot:extraTitleBox>
                <h-poptip
                  autoPlacement
                  trigger="hover"
                  customTransferClassName="apm-poptip monitor-poptip"
                >
                  <h-icon
                    name="ios-information-outl"
                    class="poptip-icon"
                  ></h-icon>
                  <div slot="content" class="pop-content" style="white-space: normal;">
                    当前交易日中，重演工具本身的发送和应答速度及资源使用情况
                  </div>
                </h-poptip>
              </template>
            </info-sum-bar>
          </div>
          <obs-table
            :title="tableTitle"
            :tableData="tableData"
            :columns="columns"
            :maxHeight="500"
            @check-change="handleCheckChange"
          >
            <template v-slot:extraTitleBox>
              <span class="table-title-slot">
                （ 核心总数：{{ instanceTotalCount }}
                <span
                  class="split-solid split-solid-table"
                ></span>
                已完成重演：{{ instanceCompletedCount }} ）
              </span>
            </template>
          </obs-table>
        </div>
        <no-data v-else-if="status && dateStatus === 'not_started'" text="当前交易日重演尚未开始，请稍候" style="height: calc(100% - 80px);"/>
        <no-data v-else text="暂无数据" style="height: calc(100% - 80px);"/>
      </div>
    </div>
  </template>
<script>
import _ from 'lodash';
import aTips from '@/components/common/apmTips/aTips';
import aTitle from '@/components/common/title/aTitle';
// import aButton from '@/components/common/button/aButton';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import obsTable from '@/components/common/obsTable/obsTable';
import {
    setReplayStart,
    setReplayStop,
    setReplayPause,
    setReplayResume,
    getReplayDetail,
    downloadInconsistencyfile
} from '@/api/coreReplayObservationApi';
import { VERIFE_STATUS_LIST, BUTTON_GROUP } from './constant';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import importStatusTableIcon from '@/components/common/icon/importStatusTableIcon.vue'; // 有用
import { transferVal, getCurrentDatetime } from '@/utils/utils';

export default {
    name: 'CoreReplayDetail',
    components: {
        obsTable,
        aTips,
        aTitle,
        // aButton,
        infoSumBar,
        aLoading,
        noData
    },
    props: {
        productInstNo: {
            type: String,
            default: ''
        },
        productList: {
            type: Array,
            default: []
        }
    },
    data() {
        const formatMethod = (val) => { return { value: transferVal(val) ? Number(val)?.toLocaleString() : '-' }; };
        // eslint-disable-next-line max-params
        const renderProgress = (h, params, progressKey, statusKey, errorMsgKey) => {
            const percent = transferVal(params?.row?.[progressKey]) ? `${params.row[progressKey]}%` : '';
            const resultObj = _.find(VERIFE_STATUS_LIST, [
                'value',
                params?.row?.[statusKey]?.toLowerCase() || ''
            ]);
            return (
                <div>
                    <importStatusTableIcon type={resultObj?.icon || ''} />
                    <span title={percent || '-'}>{percent || '-'}</span>
                    {params?.row?.[errorMsgKey] && (
                        <h-poptip autoPlacement trigger="click" customTransferClassName="apm-poptip monitor-poptip">
                            <span style="color: #2d8de5;margin: 0 5px;">错误原因</span>
                            <div slot="content" class="pop-content" style="white-space: normal; max-width: 250px">
                                {params?.row?.[errorMsgKey]}
                            </div>
                        </h-poptip>
                    )}
                </div>
            );
        };

        const renderInconsistentCount = (h, params, key) => {
            const val = transferVal(params?.row?.[key]);
            if (val === '0') {
                return <span>0</span>;
            }
            if (val && Number(val) > 0) {
                return <span title={val}>{formatMethod(val)?.value}   <h-icon name={this.downloadingIds[params?.row?.clusterName] ? 'load-c' : 't-b-download'} color="var(--link-color)" style="cursor: pointer" v-on:on-click={() => this.handleDownload(params?.row)} /></span>;
            }
            return <span>-</span>;
        };

        const renderInstanceName = (h, params, key) => {
            return <span>{params?.row?.[key] || '-'} <span class={params?.row?.role === 'master' ? 'main-flag-repaly' : ''}></span></span>;
        };

        return {
            loading: false,
            replayId: '',
            replayInstanceName: '',
            timer: null,
            tabName: '',
            taskList: [],
            taskNum: '0/0',
            label: (item) => (h) => {
                return h(
                    'div',
                    {
                        class: 'option-text'
                    },
                    [
                        h('span', {
                            class: `status status-${item?.execStatus?.toLowerCase() || ''}`,
                            style: {
                                position: 'relative',
                                top: '-2px'
                            }
                        }),
                        h('span', item.tradeDate)
                    ]
                );
            },
            selectInfoId: '',
            resultInfo: {
                title: {
                    label: '重演结果'
                },
                direction: 'grid',
                gridSpan: 2,
                details: [
                    // {
                    //     type: 'text',
                    //     title: '相同表数量',
                    //     info: {
                    //         key: 'sameTableCount',
                    //         value: '-',
                    //         formatMethod
                    //     }
                    // },
                    // {
                    //     type: 'text',
                    //     title: '不同表数量',
                    //     info: {
                    //         key: 'differentTableCount',
                    //         value: '-',
                    //         formatMethod
                    //     },
                    //     infoId: 'differentTableCount'
                    //     // canClick: true
                    // },
                    {
                        type: 'text',
                        title: '应答一致',
                        info: {
                            key: 'responseConsistentCount',
                            value: '-',
                            formatMethod
                        }
                    },
                    {
                        type: 'text',
                        title: '应答不一致',
                        info: {
                            key: 'responseInconsistentCount',
                            value: '-',
                            formatMethod
                        },
                        infoId: 'responseInconsistentCount'
                        // canClick: true
                    }
                ]
            },
            sereInfo: {
                title: {
                    label: '消息收发'
                },
                direction: 'grid',
                gridSpan: 2,
                details: [
                    {
                        type: 'text',
                        title: '应答比对进度(%)',
                        info: {
                            key: 'responseCompareProgress',
                            value: '-'
                        },
                        poptipInfo: {
                            placement: 'top',
                            title: '',
                            width: 180,
                            contentDic: {
                                pendingCompareCount: '需比对：',
                                completedCompareCount: '已比对：'
                            },
                            content: {},
                            formatMethod
                        }
                    },
                    {
                        type: 'text',
                        title: '发送进度(%)',
                        info: {
                            key: 'sendProgress',
                            value: '-'
                        },
                        poptipInfo: {
                            placement: 'top',
                            title: '',
                            width: 180,
                            contentDic: {
                                pendingSendCount: '需发送：',
                                sendCount: '已发送：'
                            },
                            content: {},
                            formatMethod
                        }
                    }
                    // {
                    //     type: 'text',
                    //     title: '应答进度(%)',
                    //     info: {
                    //         key: 'responseProgress',
                    //         value: '-'
                    //     }
                    // },
                    // {
                    //     type: 'obj',
                    //     customClassName: 'info-bar-custom',
                    //     infoDic: [
                    //         {
                    //             label: '需比对：',
                    //             key: 'pendingCompareCount',
                    //             formatMethod
                    //         },
                    //         {
                    //             label: '已比对：',
                    //             key: 'completedCompareCount',
                    //             formatMethod
                    //         }
                    //     ],
                    //     info: {}
                    // },
                    // {
                    //     type: 'obj',
                    //     customClassName: 'info-bar-custom',
                    //     infoDic: [
                    //         {
                    //             label: '需发送：',
                    //             key: 'pendingSendCount',
                    //             formatMethod
                    //         },
                    //         {
                    //             label: '已发送：',
                    //             key: 'sendCount',
                    //             formatMethod
                    //         }
                    //     ],
                    //     info: {}
                    // },
                    // {
                    //     type: 'obj',
                    //     customClassName: 'info-bar-custom',
                    //     infoDic: [
                    //         {
                    //             label: '需应答：',
                    //             key: 'requiredResponseCount',
                    //             formatMethod
                    //         },
                    //         {
                    //             label: '已应答：',
                    //             key: 'respondedCount',
                    //             formatMethod
                    //         }
                    //     ],
                    //     info: {}
                    // }
                ]
            },
            supInfo: {
                title: {
                    label: '重演工具监控'
                },
                direction: 'grid',
                gridSpan: 3,
                details: [
                    {
                        type: 'text',
                        title: '发送吞吐(tps)',
                        info: {
                            key: 'sendThroughputTps',
                            value: '-',
                            formatMethod
                        }
                    },
                    {
                        type: 'text',
                        title: '应答吞吐(tps)',
                        info: {
                            key: 'responseThroughputTps',
                            value: '-',
                            formatMethod
                        }
                    },
                    {
                        type: 'text',
                        title: '应答比对吞吐(tps)',
                        info: {
                            key: 'comparisonThroughputTps',
                            value: '-',
                            formatMethod
                        }
                    }
                    // {
                    //     type: 'text',
                    //     title: '内存(%)',
                    //     info: {
                    //         key: 'memoryPercent',
                    //         value: '-',
                    //         formatMethod
                    //     }
                    // },
                    // {
                    //     type: 'text',
                    //     title: 'CPU(%)',
                    //     info: {
                    //         key: 'cpuPercent',
                    //         value: '-',
                    //         formatMethod
                    //     }
                    // }
                ]
            },
            tableTitle: {
                label: `核心重演列表`,
                slots: [
                    {
                        type: 'checkbox',
                        label: '仅显示应答不一致',
                        key: 'revCheckbox',
                        defaultValue: false
                    }
                ]
            },
            columns: [
                {
                    title: '分片号',
                    key: 'shardingNo',
                    ellipsis: true,
                    formatMethod: (row) => (row?.shardingNo ?? '-')
                },
                {
                    title: '集群名',
                    key: 'clusterName',
                    ellipsis: true,
                    render: (h, params) => renderInstanceName(h, params, 'clusterName')
                },
                {
                    title: '发送进度',
                    key: 'sendStatus',
                    minWidth: 150,
                    render: (h, params) => renderProgress(h, params, 'sendProgress', 'sendStatus', 'sendErrorMsg'),
                    sortable: true
                },
                // {
                //     title: '应答进度',
                //     key: 'responseProgress',
                //     minWidth: 150,
                //     render: (h, params) => renderProgress(h, params, 'responseProgress', 'responseStatus', 'responseErrorMsg'),
                //     sortable: true
                // },
                {
                    title: '应答比对进度',
                    key: 'responseCompareProgress',
                    minWidth: 150,
                    render: (h, params) => renderProgress(h, params, 'responseCompareProgress', 'responseCompareStatus', 'responseCompareErrorMsg'),
                    sortable: true
                },
                {
                    title: '应答不一致数',
                    key: 'responseInconsistentCount',
                    render: (h, params) => renderInconsistentCount(h, params, 'responseInconsistentCount')
                }
                // {
                //     title: '数据比对进度',
                //     key: 'dataCompareProgress',
                //     minWidth: 150,
                //     render: (h, params) => renderProgress(h, params, 'dataCompareProgress', 'dataCompareStatus', 'dataCompareErrorMsg'),
                //     sortable: true
                // },
                // {
                //     title: '数据不一致数',
                //     key: 'dataInconsistentCount',
                //     render: (h, params) => renderInconsistentCount(h, params, 'dataInconsistentCount')
                // }
            ],
            checkboxValue: false,
            alltableData: [],
            tableData: [],
            instanceCompletedCount: 0,
            instanceTotalCount: 0,
            status: '', // 重演任务状态
            dateStatus: '', // 重演日期状态
            startTime: '', // 开始时间
            replayFilePath: '', // 重演任务相对路径
            isLoadTab: true, // 首次加载
            isRefreshing: false, // 正在刷新
            autoUpdateTime: 0, // 自动刷新倒计时时间
            autoUpdateTimeOrigin: 5, // 倒计时时间
            autoUpdateTimer: 0, // 自动刷新倒计时定时器
            // 历史吞吐
            lastTotalCount: {
                sendCount: '',
                respondedCount: '',
                completedCompareCount: ''
            },
            downloadingIds: {}
        };
    },
    computed: {
        statuslabel() {
            return _.find(VERIFE_STATUS_LIST, ['value', this.status])?.label || '-';
        }
    },
    beforeDestroy() {
        this.clearPolling();
    },
    methods: {
        async initData(id) {
            this.loading = true;
            this.isLoadTab = true;
            this.lastTotalCount = {
                sendCount: '',
                respondedCount: '',
                completedCompareCount: ''
            };
            try {
                this.clearPolling();
                this.replayId = id;
                this.autoUpdateTime = this.autoUpdateTimeOrigin;

                // 以 "交易日"维度汇集核心重演数据；进入页面时正在重演的TAB
                await this.getReplayDetail();
                this.tabName = this.taskList.filter(o => o?.execStatus?.toLowerCase() === 'running')?.[0]?.tradeDate || this.taskList?.[0]?.tradeDate || '';
                this.handleTabChange(this.tabName);
                this.isLoadTab = false;
                this.setPolling();
                this.startAutoUpdateTimer();
            } finally {
                this.loading = false;
            }
        },
        // 是否包含改按钮
        hasStatusBtn(type) {
            return BUTTON_GROUP[this.status]?.find((o) => o.action === type);
        },
        // 启动定时器查询
        setPolling() {
            this.clearPolling();
            this.timer = setInterval(async () => {
                if (this.isRefreshing || this.isLoadTab) return;
                this.isRefreshing = true;
                await this.getReplayDetail();
                this.tabName = this.tabName || this.taskList?.[0]?.tradeDate || '';
                this.handleTabChange(this.tabName);
                this.isRefreshing = false;
                clearInterval(this.autoUpdateTimer);
                this.autoUpdateTime = this.autoUpdateTimeOrigin;
                this.startAutoUpdateTimer();
            }, this.autoUpdateTimeOrigin * 1000);
        },
        startAutoUpdateTimer() {
            clearInterval(this.autoUpdateTimer);
            this.autoUpdateTimer = setInterval(() => {
                this.autoUpdateTime = this.autoUpdateTime - 1;
                if (this.autoUpdateTime === 0) {
                    this.autoUpdateTime = this.autoUpdateTimeOrigin;
                }
            }, 1000);
        },
        clearPolling() {
            this.timer && clearInterval(this.timer);
            this.autoUpdateTimer && clearInterval(this.autoUpdateTimer);
        },
        // 取消
        handleCreateCancel() {
            this.clearPolling();
            this.$emit('to-task-list', 'task-list');
        },
        // 切换Tab
        handleTabChange(tab) {
            const datail = this.taskList?.find((o) => o.tradeDate === tab) || {};
            this.alltableData = datail?.details || [];
            this.handleCheckChange(this.checkboxValue);

            this.dateStatus = datail?.execStatus?.toLowerCase() || '';
            this.replayFilePath = datail?.replayFilePath || '-';

            this.instanceCompletedCount = datail?.instanceCompletedCount || 0;
            this.instanceTotalCount = datail?.instanceTotalCount || 0;

            this.resultInfo.details.forEach((v) => {
                v.info.value = datail?.[v.info.key];
            });

            this.sereInfo.details[0].info.value =  datail?.responseCompareProgress;
            this.sereInfo.details[0].poptipInfo.content = {
                pendingCompareCount: datail?.pendingCompareCount,
                completedCompareCount: datail?.completedCompareCount
            };

            this.sereInfo.details[1].info.value =  datail?.sendProgress;
            this.sereInfo.details[1].poptipInfo.content = {
                pendingSendCount: datail?.pendingSendCount,
                sendCount: datail?.sendCount
            };

            // 吞吐计算
            this.supInfo.details[0].info.value = this.calcThroughput(datail.sendCount, this.lastTotalCount.sendCount);
            this.supInfo.details[1].info.value = this.calcThroughput(datail.respondedCount, this.lastTotalCount.respondedCount);
            this.supInfo.details[2].info.value = this.calcThroughput(datail.completedCompareCount, this.lastTotalCount.completedCompareCount);

            this.$nextTick(() => {
                this.lastTotalCount = {
                    sendCount: datail?.sendCount,
                    respondedCount: datail?.respondedCount,
                    completedCompareCount: datail?.completedCompareCount
                };
            });

        },
        // 计算吞吐
        calcThroughput(current, last) {
            if (!transferVal(current) || !transferVal(last)) {
                return '-';
            }
            // 计算吞吐量
            const value = (current - last) / this.autoUpdateTimeOrigin;
            return value.toFixed(2);
        },
        // 更新表格数据
        updateTableData() {
            const tableData = [...this.alltableData];
            this.tableData = this.checkboxValue
                ? _.filter(tableData, o => transferVal(o?.responseInconsistentCount) && Number(o?.responseInconsistentCount) > 0)
                : tableData;
        },
        // 点击事件
        async handleBarClick(id) {
            this.selectInfoId = id;
            if (!id) return;
        },
        // 核心不一致
        handleCheckChange(value) {
            this.checkboxValue = value;
            this.updateTableData();
        },
        // 接口
        async getReplayDetail() {
            try {
                const param = {
                    id: this.replayId
                };
                const res = await getReplayDetail(param);
                if (res.code === '200') {
                    this.taskList = res?.data?.infos || [];
                    this.status = res?.data?.execStatus?.toLowerCase() || '';
                    this.startTime = res?.data?.startTime || '';
                    this.replayInstanceName = res?.data?.replayInstanceName || '';
                    this.taskNum = (res?.data?.tradeDateCompletedCount ?? 0) + '/' + (res?.data?.tradeDateTotalCount ?? 0);
                } else {
                    this.taskList = [];
                    this.startTime = '';
                    this.status = '';
                    this.replayInstanceName = '';
                    this.taskNum = '0/0';
                    this.clearPolling();
                }
            } catch (err) {
                console.error(err);
                this.clearPolling();
            }
        },
        // 操作按钮
        async onButtonClick(action) {
            const params = {
                id: this.replayId
            };
            const actions = {
                start: {
                    api: setReplayStart,
                    successMessage: '启动成功'
                },
                terminated: {
                    api: setReplayStop,
                    successMessage: '终止成功'
                },
                paused: {
                    api: setReplayPause,
                    successMessage: '暂停成功'
                },
                resume: {
                    api: setReplayResume,
                    successMessage: '恢复成功'
                }
            };

            if (actions[action]) {
                const { api, successMessage } = actions[action];
                const res = await api(params);
                if (res?.code === '200') {
                    this.$hMessageSafe.success(successMessage);
                    await this.initData(this.replayId);
                } else if (res?.code?.length === 8) {
                    this.$hMessageSafe.error(res?.message);
                }
            }
        },
        // 下载zip
        async handleDownload(row){
            if (this.downloadingIds[row?.clusterName]) return; // 如果正在下载中则不重复下载
            const params = {
                replayId: this.replayId,
                clusterName: row?.clusterName
            };
            try {
                this.$set(this.downloadingIds, row.clusterName, true); // 设置下载状态
                const res = await downloadInconsistencyfile(params);
                if (res?.type !== 'application/json') {
                    const url = URL.createObjectURL(res);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `${this.replayInstanceName}_${row?.clusterName}_${getCurrentDatetime()}.zip`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    this.$hMessage.success('下载成功!');
                } else if (res?.type === 'application/json') {
                    const text = await res.text();
                    const json = JSON.parse(text);
                    this.$hMsgBoxSafe.error({
                        title: '下载失败',
                        content: json?.message
                    });
                }
            } catch (error) {
                this.$hMessage.error(error);
            } finally {
                this.$set(this.downloadingIds, row.clusterName, false); // 清除下载状态
            }
        }
    }
};
</script>

  <style lang="less">
    .core-replay-detail-box {
        .status {
            display: inline-block;
            border-radius: 3px;
            height: 6px;
            margin-right: 5px;
            width: 6px;
            top: -1px;
            position: relative;
        }

        /* stylelint-disable-next-line selector-class-pattern */
        .status-not_started {
            background: #9296a1;
        }

        .status-stopped {
            background: #f5222d;
        }

        .status-running {
            background: #2d8de5;
        }

        .status-finished {
            background: #52c41a;
        }

        .status-paused {
            background: #ff9901;
        }

        .status-failed {
            background: #f5222d;
        }

        .main-flag-repaly {
            display: inline-block;
            width: 11px;
            height: 11px;
            content: "";
            background: url("static/mainFlag.png");
            background-size: 11px 10px;
            background-repeat: no-repeat;
            position: relative;
            top: 2px;
        }
    }
  </style>
  <style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");
@import url("@/assets/css/poptip-1.less");

.core-replay-detail-box {
    width: 100%;
    height: 100%;

    .split-solid {
        width: 1px;
        background-color: #474e6f;
        height: 26px;
        display: inline-block;
        margin-left: 10px;
        margin-right: 10px;
    }

    .split-solid-table {
        height: 12px;
        margin-left: 5px;
        margin-right: 5px;
        position: relative;
        top: 3px;
    }

    .h-poptip,
    .h-poptip-rel {
        margin-left: 3px;
    }

    .title {
        min-width: 800px;

        .slot-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            top: -42px;
            right: 10px;
        }

        .create-title {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .h-icon {
                cursor: pointer;
                margin-right: 8px;

                &:hover {
                    color: var(--link-color);
                }
            }
        }

        .product-select {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .h-select {
                width: 200px;
            }
        }

        .apm-title-create {
            &::before {
                display: none;
            }
        }
    }

    .product-box {
        min-width: 800px;
        height: calc(100% - 53px);
        margin-top: 10px;
        border-radius: 4px;

        /deep/.h-tabs {
            height: 0;
        }

        .tab-box {
            /deep/ .h-tabs-nav-container {
                height: 42px;
                width: 100%;
            }

            /deep/ .h-tabs-bar {
                margin-bottom: 10px;
                border-bottom: var(--border);
            }

            /deep/ .h-tabs-tab {
                padding: 10px 6px;
            }

            /deep/ .h-tabs-content-wrap {
                height: 0;
            }
        }

        /deep/.info-bar.info-bar-custom {
            background: var(--wrapper-color);

            .info-bar-detail {
                padding: 10px 5px;
                line-height: 24px;
                font-size: 12px;
            }
        }
    }

    .table-title-slot {
        color: var(--font-body-color);
        font-size: 12px;
    }

    .info-bar-box {
        display: grid;
        grid-template-columns: 29% 29% 1fr;
        grid-auto-rows: row dense;
        grid-gap: 0 10px;
    }

    /deep/ .h-icon-load-c {
        animation: ani-loading-spin 1s linear infinite;
        display: inline-block;
    }

    @keyframes ani-loading-spin {
        from {
            transform: rotate(0deg);
        }

        25% {
            transform: rotate(90deg);
        }

        50% {
            transform: rotate(180deg);
        }

        75% {
            transform: rotate(270deg);
        }

        to {
            transform: rotate(360deg);
        }
    }
}
  </style>
