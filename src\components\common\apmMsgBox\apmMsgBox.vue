<!--
    APM自定义弹窗：
        当前支持两种类型内容：text和table

    @参数说明：modalInfo对象中
        status: 弹窗状态
        width: 自定义弹窗宽度
        title：弹窗标题文字
        contentType：内容类型 （'text' || 'table' || 'obj'）
        contentText: 文字内容 （contentType = 'text'需传）
        columns：表格表头 （contentType = 'table'需传）
        tableData：表格数据 （contentType = 'table'需传）
        configKey：弹窗需确认时，抛出 "msgbox-config" 事件 传参configKey作为弹窗唯一标识。（该参数不传弹窗只有关闭按钮）
 -->
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :closable="false" :mask-closable="false" :width="modalData.width" maxHeight="350" allowCopy>
            <template v-slot:header>
                <div class="header-info">
                    <h-icon :name="modalData.configKey ? 'feedback' : 'prompt'" color="var(--warning-color)" size=28></h-icon>
                    <span class="title-info">{{modalData.title}}</span>
                </div>
            </template>
            <div v-if="modalData.contentType" class="content-body">
                <div v-if="modalData.contentType === 'text'" class="text-body">{{modalData.contentText}}</div>
                <h-table v-else-if="modalData.contentType === 'table'" showTitle :columns="modalData.columns" :data="modalData.tableData" :maxHeight="300" :loading="loading"></h-table>
                <div v-else-if="modalData.contentType === 'obj'" class="obj-body">
                    <p v-for="info in modalData.contentDic" :key="info.key">
                        <span :title="info.title">{{info.title}}</span>
                        <span :title="modalData.contentObj[info.key]">
                            {{modalData.contentObj[info.key]}}
                        </span>
                    </p>
                </div>
            </div>
            <template v-slot:footer>
                <div>
                    <a-button @click="modalData.status = false">{{modalData.configKey ? $t('common.cancel') : $t('common.close')}}</a-button>
                    <a-button v-if="modalData.configKey" type="primary" @click="submitConfig">{{modalData.configKey || $t('common.confirm')}}</a-button>
                </div>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    name: 'ApmMsgBox',
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo
        };
    },
    mounted(){
        this.loading = true;
        setTimeout(() => {
            this.loading = false;
        }, 300);
    },
    methods: {
        submitConfig() {
            this.$emit('msgbox-config', this.modalData.configKey, this.modalData.configData);
            this.modalData.status = false;
        }
    }
};
</script>

<style lang="less" scoped >
.header-info {
    font-weight: 500;
    display: flex;

    & > .h-icon {
        position: relative;
        top: 2px;
    }

    .title-info {
        line-height: 30px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

/deep/ .h-modal-header {
    border: none;
}

/deep/ .h-modal-footer {
    border: none;
}

/deep/ .h-modal-body {
    padding: 16px 32px;
}

/deep/ .h-table-row-checked td {
    background: #fff;
}

.content-body {
    .text-body {
        font-size: 12px;
        color: #24262b;
        text-align: left;
        line-height: 20px;
        padding-left: 18px;
        position: relative;
    }

    .obj-body {
        color: var(--font-color);
        line-height: 30px;
        width: 100%;

        p {
            display: flex;

            span {
                width: 50%;
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}
</style>
