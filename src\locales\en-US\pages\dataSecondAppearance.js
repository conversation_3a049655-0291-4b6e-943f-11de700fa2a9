export default {

    dataSecondAppearance: 'Data Second Appearance',
    createSecondAppearanceTask: 'Create Second Appearance Task',
    pleaseSelect: 'Please Select',

    // constant
    append: 'Append',
    overwrite: 'Overwrite',
    appendAndOverwrite: 'Append+Overwrite',
    pending: 'Pending',
    running: 'Running',
    succeeded: 'Succeeded',
    failed: 'Failed',
    warn: 'Warning',
    success: 'Success',
    error: 'Error',
    selectTableTip: 'Select the tables needed for second appearance as required',
    confirmRuleTip: 'Determine the loading mode and rules for table appearance (If no rules are specified, the entire table will be loaded by default)',
    reviewTip: 'Please carefully review the content, loading mode, and rules of the second appearance. Click "Submit" if there are no errors.',
    // createAppearanceTask
    batchUpdateMode: 'Batch update appearance mode',
    tableName: 'Table name',
    cluster: 'Cluster',
    sharding: 'Sharding',
    loadMode: 'Load mode',
    appearanceRule: 'Appearance rule',
    enterRule: 'Enter appearance rule',
    enterFilterTip: 'Individual settings for the query conditions of appearance data for each table is possible; absence of rules indicates loading the entire table.',
    confirmAppearanceContent: 'Confirm appearance content',
    configAppearanceRule: 'Configure appearance rule',
    infoCheck: 'Info check',
    previousStep: 'Previous step',
    nextStep: 'Next step:',
    appearance: 'Appearance',
    cancel: 'Cancel',
    executingAppearance: 'Executing appearance!',
    appearanceFailed: 'Appearance execution failed!',
    selectAppearanceTableWarning: 'Please select the tables needed for second appearance',
    availableTables: 'Available tables',
    selectedTables: 'Selected tables',
    searchPlaceholder: 'Search by table name/cluster/sharding',
    // secondAppearanceDetail
    appearanceResult: 'Appearance result',
    appearanceStatusAndResult: 'Appearance status and result',
    errorMessage: 'Error message',
    appearanceTime: 'Appearance time',
    executionStatus: 'Execution status',
    endTime: 'End time',
    viewReason: 'View reason',
    appearanceTableCount: 'Number of tables needed for appearance',
    appearanceHistory: 'Appearance history',
    latestAppearance: 'Latest appearance',
    failedReason: 'Failed reason',
    createTask: 'Create task',
    noData: 'No data'
};
