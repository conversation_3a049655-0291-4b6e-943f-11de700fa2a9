<template>
    <h-icon v-if="type === 'loading'" name="load-c" color="#2D8DE5" class="loading-icon-load a-icon"></h-icon>
    <h-icon v-else-if="type === 'success'" name="success1" color="#4ECA89" class="a-icon"></h-icon>
    <h-icon v-else-if="type === 'successGray'" name="success1" color="#9296A1" class="a-icon"></h-icon>
    <h-icon v-else-if="type === 'error'" name="error" color="#F5222D" class="a-icon"></h-icon>
    <h-icon v-else-if="type === 'offline'" name="t-b-offline" size="15" color="#999999" class="a-icon"></h-icon>
    <h-icon v-else-if="type === 'warn'" name="prompt1" color="#ff9901" size="14" class="a-icon"></h-icon>
    <span v-else-if="type === 'paused'" class="paused-icon"><svg t="1741917280313" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2691" width="20" height="20" data-spm-anchor-id="a313x.search_index.0.i8.62833a81DoZBTQ"><path d="M533.333333 896C332.8 896 170.666667 733.866667 170.666667 533.333333S332.8 170.666667 533.333333 170.666667 896 332.8 896 533.333333 733.866667 896 533.333333 896z m0-42.666667c174.933333 0 320-145.066667 320-320S708.266667 213.333333 533.333333 213.333333 213.333333 358.4 213.333333 533.333333 358.4 853.333333 533.333333 853.333333zM469.333333 426.666667v213.333333h-42.666666v-213.333333h42.666666z m170.666667 0v213.333333h-42.666667v-213.333333h42.666667z" fill="#FF9901" p-id="2692"></path></svg></span>
    <span v-else-if="type === 'stop'" class="stop-icon"><svg t="1744878096788" class="icon" viewBox="0 0 1088 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3530" width="18" height="18"><path d="M518.08 64c249.6 0 448 198.4 448 448s-198.4 448-448 448-448-198.4-448-448 198.4-448 448-448z m0 64c-211.2 0-384 172.8-384 384s172.8 384 384 384 384-172.8 384-384-172.8-384-384-384z m128 236.8c6.4 0 12.8 6.4 12.8 12.8V640c0 6.4-6.4 12.8-12.8 12.8h-262.4c-6.4 0-12.8-6.4-12.8-12.8V377.6c0-6.4 6.4-12.8 12.8-12.8h262.4z" p-id="3531" fill="#d81e06"></path></svg></span>
    <span v-else></span>
</template>

<script>
export default {
    name: 'SqlTableIcon',
    props: {
        type: {
            type: String,
            default: ''
        }
    }
};
</script>

<style lang="less" scoped>
.a-icon {
    // margin-top: 2px;
    margin-right: 5px;
    font-size: 14px;
    display: inline-block;
    vertical-align: middle;
    line-height: normal;
}

.stop-icon {
    margin-right: 2px;
    font-size: 0;
    margin-left: -3px;
    display: inline-block;
    vertical-align: -5px;
    line-height: normal;
}

.paused-icon {
    margin-right: 2px;
    font-size: 0;
    margin-left: -3px;
    display: inline-block;
    line-height: normal;
    vertical-align: -5px;
}

.loading-icon-load {
    animation: ani-loading-spin 1s linear infinite;
    display: inline-block;
}

@keyframes ani-loading-spin {
    from {
        transform: rotate(0deg);
    }

    25% {
        transform: rotate(90deg);
    }

    50% {
        transform: rotate(180deg);
    }

    75% {
        transform: rotate(270deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>
