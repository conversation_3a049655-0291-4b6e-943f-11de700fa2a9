<!--
 * @Description: 执行记录侧弹窗
-->
<template>
    <div>
        <h-drawer
            ref="drawer-box"
            v-model="modalData.status"
            title="执行记录"
            width="60"
            @on-close="handleDrawerClose">
            <!-- 基础信息 -->
            <div ref="basic-info" class="basic-info">
                <h-row>
                    <h-col :span="12">
                        <span class="info-label">源数据库:</span>
                        <span class="info-value">{{ modalData.sourceDatabaseName || '-' }}</span>
                    </h-col>
                    <h-col :span="12">
                        <span class="info-label">目标数据库:</span>
                        <span class="info-value">{{ modalData.targetDatabasesName || '-' }}</span>
                    </h-col>
                </h-row>
                <h-row>
                    <h-col :span="12">
                        <span class="info-label">校验类型:</span>
                        <span class="info-value">{{ ruleTypeLabel }}</span>
                    </h-col>
                    <h-col :span="12">
                        <span class="info-label">错误阈值:</span>
                        <span class="info-value">{{ modalData.errorThreshold || '-' }}</span>
                    </h-col>
                </h-row>
                <h-row>
                    <span class="info-label">校验内容:</span>
                    <span class="info-value">{{ modalData.verificationContent || '-' }}</span>
                </h-row>
            </div>
            <!-- 执行历史表格 -->
            <h-simple-table
                :columns="columns"
                :data="tableData"
                showTitle
                :loading="loading"
                :height="tableHeight">
            </h-simple-table>
        </h-drawer>

        <!-- 查看直接结果详情 -->
        <verify-detail-modal
            v-if="resultInfo.status"
            :productId="productId"
            :modalInfo="resultInfo">
        </verify-detail-modal>
    </div>
</template>

<script>
import _ from 'lodash';
import { autoConvertTimeRender } from '@/utils/utils';
import { VERIFE_TYPE_LIST, VERIFE_RESULT_LIST } from '@/components/ustTableVerification/constant';
import { getFieldCompareHistories, getRecordCompareHistories } from '@/api/dataVerification';
import importStatusTableIcon from '@/components/common/icon/importStatusTableIcon.vue';
import verifyDetailModal from '@/components/ustTableVerification/modal/verifyDetailModal.vue';
export default {
    name: 'ExecutionRecord',
    components: { verifyDetailModal },
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        }
    },
    computed: {
        ruleTypeLabel() {
            return VERIFE_TYPE_LIST.find(e => e.value === this.modalData.ruleType)?.label || '-';
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: true,
            columns: [
                {
                    title: '执行时间',
                    key: 'startTime',
                    minWidth: 145,
                    ellipsis: true
                },
                {
                    title: '校验耗时',
                    key: 'spendTime',
                    ellipsis: true,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'spendTime', 's');
                    }
                },
                {
                    title: '源表数据总数',
                    key: 'mainTotalTableRecordCount',
                    minWidth: 130,
                    ellipsis: true,
                    formatMethod: text => text.mainTotalTableRecordCount ?? '-',
                    hiddenCol: this.modalInfo.ruleType !== 'tableRecordCount'
                },
                {
                    title: '目标表数据总数',
                    key: 'compareTotalTableRecordCount',
                    minWidth: 130,
                    ellipsis: true,
                    formatMethod: text => text.compareTotalTableRecordCount ?? '-',
                    hiddenCol: this.modalInfo.ruleType !== 'tableRecordCount'
                },
                {
                    title: '差值',
                    key: 'diffCount',
                    minWidth: 140,
                    ellipsis: true,
                    render: (h, params) => {
                        const difference = this.handleDifference(params.row.mainTotalTableRecordCount, params.row.compareTotalTableRecordCount);
                        return h('div', {
                            attrs: {
                                title: difference ?? '-'
                            }
                        }, difference ?? '-');
                    },
                    hiddenCol: this.modalInfo.ruleType !== 'tableRecordCount'
                },
                {
                    title: '校验不一致字段数',
                    key: 'recordFailCount',
                    minWidth: 140,
                    hiddenCol: this.modalInfo.ruleType !== 'tableField',
                    render: (h, params) => {
                        const value = params.row.recordFailCount;
                        return h('span', {
                            attrs: {
                                title: value ?? '-'
                            }
                        }, value ?? '-');
                    }
                },
                {
                    title: '校验结果',
                    key: 'compareResult',
                    minWidth: 110,
                    render: (h, params) => {
                        const resultObj = _.find(VERIFE_RESULT_LIST, ['value', params.row.compareResult]);
                        // 若图标为 warn 和 error，label文字需要支持点击
                        const canClick = ['warn', 'error'].includes(resultObj?.icon);
                        return <div title={resultObj?.label || '-'}>
                            <importStatusTableIcon type={resultObj?.icon || ''}/>
                            {
                                canClick
                                    ? <h-button type="text"
                                        style="padding: 0 0 2px 0; color: var(--link-color)"
                                        onClick={() => this.handleViewError(params.row)}>
                                        {resultObj?.label || '-'}
                                    </h-button>
                                    : <span>{resultObj?.label || '-'}</span>
                            }
                        </div>;
                    }
                }
            ],
            tableData: [],
            tableHeight: 0,
            downExecId: '',
            resultInfo: {
                status: false
            }
        };
    },
    mounted() {
        this.handleDrawerOpen();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        /**
         * 计算两个数差绝对值
         */
        handleDifference(val1, val2) {
            // 检查两个值是否为数字
            const isValidNumber = (num) => typeof num === 'number' && !isNaN(num);
            if (!isValidNumber(val1) || !isValidNumber(val2)) {
                return '-';
            }
            // 计算差值 - 绝对值
            return Math.abs(val1 - val2);
        },
        // 规则比对结果列表
        async getCompareRuleHistories() {
            const param = {
                productId: this.productId,
                ruleId: this.modalData.ruleId
            };
            try {
                const apiFunc = this.modalData.ruleType === 'tableRecordCount' ? getRecordCompareHistories : getFieldCompareHistories;
                const res = await apiFunc(param);
                if (res.code === '200'){
                    this.tableData = [...res?.data || []];
                }
            } catch (err) {
                console.error(err);
            }
            this.loading = false;
        },
        // 处理校验异常场景
        handleViewError(row) {
            // 查看详情弹窗
            this.resultInfo = {
                status: true,
                ruleId: this.modalData.ruleId,
                ruleType: this.modalData.ruleType,
                ruleName: this.modalData.ruleName,
                ...row
            };
        },
        // 打开弹窗-获取数据
        async handleDrawerOpen() {
            await this.getCompareRuleHistories();
            this.fetTableHeight();
        },
        // 关闭侧弹窗 清理数据
        handleDrawerClose() {
            this.modalData.status = false;
            this.tableData = [];
        },
        fetTableHeight() {
            this.$nextTick(() => {
                const infoHeight = this.$refs['basic-info']?.offsetHeight || 90;
                const subHeight = infoHeight + 102;
                this.tableHeight = this.$refs['drawer-box']?.$el?.offsetTop - subHeight;
            });
        }
    }
};
</script>

<style lang="less" scoped>
.basic-info {
    max-height: 200px;
    background: #f7f7f7;
    padding: 10px 15px;
    margin-bottom: 10px;
    overflow: auto;

    .h-row {
        min-height: 25px;
    }

    .info-label {
        color: #505050;
    }

    .info-value {
        color: #000;
    }
}

.time-info {
    height: 32px;
    line-height: 32px;
    background: #f7f7f7;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 0 12px;
    color: #999;
}

.bold-text {
    color: #333;
    font-weight: 500;

    & > .h-icon {
        position: relative;
        top: 2px;
    }
}

.fail-info {
    line-height: 32px;
    padding: 0 12px;
    color: #777;
}

.msg-info {
    text-align: right;
    margin-bottom: 10px;

    .h-checkbox-wrapper {
        margin-right: 15px;
        display: inline-block;
    }

    .h-input-wrapper {
        max-width: 200px;
        display: inline-block;
    }

    .h-input-icon {
        cursor: pointer;

        &:hover {
            color: var(--link-color);
        }
    }
}
</style>
