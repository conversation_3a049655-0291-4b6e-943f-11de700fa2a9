/*
 * @Description: 组合查询组件
 * @Author: xiangyl51186
 * @Date: 2024-01-28 13:52:31
 * @LastEditTime: 2024-01-30 15:20:49
 * @LastEditors: xiangyl51186 <EMAIL>
 */
import './apmQueryGroup.less';
import { COMPONENT_TYPE } from './constant';
import { convertValueFromObject, initValueObject } from './util';

export default {
    name: 'apmQueryGroup',
    emits: ['onChange'],
    model: {
        event: 'onChange'
    },
    props: {
        config: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            valueObject: {}
        };
    },
    mounted() {
        this.valueObject = initValueObject(this.config.items);
    },
    methods: {
        onChangeInput(e, key) {
            this.valueObject[key] = e.target.value;
            this.$emit('onChange', this.valueObject);
        },
        onChangeSelect(val, key) {
            this.valueObject[key] = val;
            this.$emit('onChange', this.valueObject);
        },
        reset() {
            this.valueObject = initValueObject(this.config.items);
            this.$emit('onChange', this.valueObject);
        },
        /**
     * 向外暴露获取数据的方法
     */
        getValue() {
            return convertValueFromObject(this.config.items, this.valueObject);
        },

        /**
     * @param {IQueryItem} item
     */
        renderItem(item) {
            switch (item.type) {
                case COMPONENT_TYPE.INPUT:
                    return (
                        <h-input
                            v-model={this.valueObject[item.key]}
                            value={this.valueObject[item.key]}
                            type={item.inputType || 'text'}
                            v-on:on-change={(e) => this.onChangeInput(e, item.key)}
                            placeholder={item.placeholder}
                        />
                    );
                case COMPONENT_TYPE.SELECT:
                    return (
                        <h-select
                            v-model={this.valueObject[item.key]}
                            value={this.valueObject[item.key]}
                            clearable={false}
                            positionFixed
                            v-on:on-change={(e) => this.onChangeSelect(e, item.key)}
                        >
                            {item.options?.map((option) => (
                                <h-option value={option.value} key={option.value}>
                                    {option.label}
                                </h-option>
                            ))}
                        </h-select>
                    );

                default: {
                    console.warn(this.$t('pages.common.unsupportedComponentError', { type: item.type }));
                    return <span></span>;
                }
            }
        }
    },
    render() {
        return (
            <div class="container">
                <div class="container-label">{this.config.label}</div>
                {this.config.items?.map((item) => (
                    <div
                        class="container-item"
                        style={{
                            flex: item.width ? 'none' : 1,
                            width: item.width ?? 'auto'
                        }}
                    >
                        {this.renderItem(item)}
                    </div>
                ))}
            </div>
        );
    }
};
