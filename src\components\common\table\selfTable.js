// 前端假分页、支持表格指定列内容搜索
import './table.less';
import apmSelectSearch from '@/components/common/apmSelectSearch/apmSelectSearch';
export default {
    name: 'self-table',
    components: { apmSelectSearch },
    props: {
        searchType: {
            type: String,
            default: 'input' // 'select'
        },
        placeholder: {
            type: String,
            default: this.$t('pages.common.enterKeyword')
        },
        hasDarkClass: {
            type: Boolean,
            default: true
        },
        multiLevel: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: []
        },
        tableData: {
            type: Array,
            default: []
        },
        border: {
            type: Boolean,
            default: false
        },
        showTitle: {
            type: Boolean,
            default: false
        },
        hasPage: {
            type: Boolean,
            default: true
        },
        showSizer: {
            type: Boolean,
            default: true
        },
        showElevator: {
            type: Boolean,
            default: true
        },
        simple: {
            type: Boolean,
            default: false
        },
        height: {
            type: Number | String | undefined,
            default: undefined
        },
        maxHeight: {
            type: Number | String | undefined,
            default: undefined
        },
        loading: {
            type: Boolean,
            default: false
        },
        disabledHover: {
            type: Boolean,
            default: false
        },
        highlightRow: {
            type: Boolean,
            default: false
        },
        immediateRowClick: {
            type: Boolean,
            default: false
        },
        rowSelectOnly: {
            type: Boolean,
            default: false
        },
        notSetWidth: {
            type: Boolean,
            default: false
        },
        autoHeadWidth: {
            type: Boolean,
            default: false
        },
        showTotal: {
            type: Boolean,
            default: false
        },
        canDrag: {
            type: Boolean,
            default: false
        },
        hPageSize: {
            type: String || undefined,
            default: undefined
        },
        isBlur: {
            type: Boolean,
            default: false
        },
        noDataText: {
            type: String,
            default() {
                return this.$t('common.noData');
            }
        },
        searchKey: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            page: 1,
            pageSize: 10,
            sizerOption: {
                transfer: true,
                autoPlacement: true
            },
            sortField: '',
            sortType: '',
            currentPagedData: [],
            searchQuery: '',
            total: 0
        };
    },
    computed: {
        searchList() {
            if (this.searchType !== 'select' || !this.searchKey) {
                return [];
            }
            const searchData = [...new Set(this.tableData.map(o => o?.[this.searchKey]) || [])];
            const data = searchData.map(value => ({ label: value, value }))?.sort((a, b) => a?.label?.localeCompare(b?.label));
            return data;
        }
    },
    watch: {
        tableData: {
            immediate: true,
            handler: 'updatePagedData'
        },
        page: 'updatePagedData',
        pageSize: 'updatePagedData',
        searchQuery: 'updatePagedData'
    },
    methods: {
        init(){
            this.resetPage();
            this.resetPageSize();
            this.updatePagedData();
        },
        updatePagedData() {
            let filteredData = this.tableData;
            if (this.searchQuery && this.searchKey) {
                filteredData = this.tableData.filter(item =>
                    String(item[this.searchKey]).toLowerCase().includes(this.searchQuery.toLowerCase())
                );
            }
            const pageCount = Math.ceil(filteredData.length / this.pageSize);
            if (this.page > pageCount && pageCount > 0) {
                this.page = 1;
            }
            const start = (this.page - 1) * this.pageSize;
            const end = this.page * this.pageSize;
            this.currentPagedData = filteredData.slice(start, end);
            this.total = filteredData.length;
        },
        resetPage() {
            this.page = 1;
        },
        resetPageSize() {
            this.pageSize = 10;
        },
        resetPageElevator() {
            this.$refs.page.clearElevator();
        },
        pageChange(page) {
            this.page = page;
            this.updatePagedData();
        },
        pageSizeChange(size) {
            this.page = 1;
            this.pageSize = size;
            this.updatePagedData();
        },
        rowClick(row) {
            this.$emit('rowClick', row);
        },
        onCurrentChange(row) {
            this.$emit('onCurrentChange', row);
        },
        tableSelection(selection) {
            this.$emit('selection', selection);
        },
        exportCsv(param) {
            this.$refs.table.exportCsv(param);
        },
        handleSearch(event) {
            this.searchQuery = event.target.value;
            this.resetPage();
            this.updatePagedData();
        },
        handleSelectChange(val){
            this.searchQuery = val;
            this.resetPage();
            this.updatePagedData();
        }
    },
    render() {
        return (
            <div class={this.hasDarkClass ? 'a-table self-table' : ''}>
                <div class='search-box'>
                    {
                        this.searchType === 'input' ? <h-input
                            type="text"
                            style="width: 200px;"
                            placeholder={this.placeholder}
                            value={this.searchQuery}
                            v-on:on-blur={this.handleSearch}
                            clearable={true}
                        /> :  this.searchType === 'select'
                            ? <apm-select-search
                                minWidth="150px"
                                focusWidth="350px"
                                dropHeight="200px"
                                list={this.searchList}
                                clearable
                                placeholder={this.placeholder}
                                border={true}
                                v-on:onChange={this.handleSelectChange}
                            /> : ''
                    }
                </div>
                <h-table
                    id={this.multiLevel?.length ? 'multi' : ''}
                    ref="table"
                    multiLevel={this.multiLevel}
                    columns={this.columns}
                    data={this.currentPagedData}
                    border={this.border}
                    showTitle={this.showTitle}
                    height={this.height}
                    maxHeight={this.maxHeight}
                    loading={this.loading}
                    canDrag={this.canDrag}
                    v-on:on-row-click={this.rowClick}
                    disabledHover={this.disabledHover}
                    highlightRow={this.highlightRow}
                    immediateRowClick={this.immediateRowClick}
                    v-on:on-current-change={this.onCurrentChange}
                    rowSelectOnly={this.rowSelectOnly}
                    notSetWidth={this.notSetWidth}
                    autoHeadWidth={this.autoHeadWidth}
                    v-on:on-selection-change={this.tableSelection}
                    noDataText={this.noDataText}
                ></h-table>
                {!this.loading ? (
                    <h-page
                        class='page-box'
                        ref='page'
                        total={this.total}
                        current={this.page}
                        pageSize={this.pageSize}
                        v-on:on-change={this.pageChange}
                        v-on:on-page-size-change={this.pageSizeChange}
                        showSizer={this.showSizer}
                        showTotal={this.showTotal}
                        showElevator={this.showElevator}
                        isBlur={this.isBlur}
                        sizerOption={this.sizerOption}
                        simple={this.simple}
                        size={this.hPageSize}
                    ></h-page>
                ) : (
                    ''
                )}
            </div>
        );
    }
};
