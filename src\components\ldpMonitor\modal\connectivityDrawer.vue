<template>
    <div>
        <h-drawer
            ref="drawer-box"
            v-model="modalData.status"
            className="dark-drawer"
            :title="drawerTitle"
            height="40"
            placement="down"
            :mask-closable="true"
            @on-close="handleDrawerClose">
            <div>
                <a-table
                    :columns="columns"
                    :tableData="tableData"
                    :loading="loading"
                    :hasPage="false"
                    showTitle
                    :height="tableHeight">
                </a-table>
            </div>
        </h-drawer>

        <!-- 会话信息浮层 -->
        <session-tip-layer
            ref="tip-box"
            :productId="productId">
        </session-tip-layer>
    </div>
</template>

<script>
import { getRcmConfigList } from '@/api/productApi';
import { getTopoConnections } from '@/api/topoApi';
import aTable from '@/components/common/table/aTable';
import SessionTipLayer from './sessionTipLayer.vue';
const DRAW_TILE_ENUM = {
    service: '全局总览',
    cluster: '集群连接信息',
    instance: '应用连接信息',
    context: '上下文连接信息'
};
const JUMP_TAB_ENUM = {
    service: 'appClusterTopology',
    cluster: 'appInstanceTopology',
    instance: 'contextTopology'
};
export default {
    name: 'ConnectivityDrawer',
    components: { aTable, SessionTipLayer },
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: () => {}
        }
    },
    computed: {
        // 节点类型
        nodeType() {
            return this.modalData.nodeType || '';
        },
        // 连接状态字典
        connectStatusDict() {
            return this.$store?.state?.apmDirDesc?.connectStatusDict || {};
        },
        // 应用类型、集群、应用节点表头
        insColumns() {
            return [
                {
                    title: this.nodeType === 'service' ? '服务名' : '发送端',
                    key: 'senderNodeName',
                    ellipsis: true
                },
                {
                    title: 'RCM通讯个数',
                    key: 'rcmConnectCount',
                    render: (h, params) => {
                        return h('div', {
                            style: {
                                display: 'flex',
                                'align-items': 'center',
                                position: 'relative'
                            }
                        }, [
                            params.row?.supportRcmDrillDown
                                ? h('a', {
                                    on: {
                                        click: () => {
                                            this.handleDrillDown(params.row, 'rcm');
                                        }
                                    }
                                }, params.row.rcmConnectCount)
                                : h('span', params.row.rcmConnectCount),
                            // 应用节点可点击查看
                            !!(this.nodeType === 'instance' && params.row?.rcmConnectCount) &&
                            h('h-icon', {
                                props: {
                                    name: 'search',
                                    color: '#2d8de5'
                                },
                                style: {
                                    'margin-left': '3px',
                                    cursor: 'pointer'
                                },
                                on: {
                                    'on-click': (event) => {
                                        this.handleShowSessionInfo(event, {
                                            ...params.row,
                                            connectType: 'RCM'
                                        });
                                    }
                                }
                            })
                        ]);
                    }
                },
                {
                    title: 'TCP通讯个数',
                    key: 'tcpConnectCount',
                    render: (h, params) => {
                        return h('div', {
                            style: {
                                display: 'flex',
                                'align-items': 'center'
                            }
                        }, [
                            params.row?.supportTcpDrillDown
                                ? h('a', {
                                    on: {
                                        click: () => {
                                            this.handleDrillDown(params.row, 'tcp');
                                        }
                                    }
                                }, params.row.tcpConnectCount)
                                : h('span', params.row.tcpConnectCount),
                            // 应用节点展示放大镜图标可点击查看poptip
                            !!(this.nodeType === 'instance' && params.row?.tcpConnectCount) &&
                            h('h-icon', {
                                props: {
                                    name: 'search',
                                    color: '#2d8de5'
                                },
                                style: {
                                    'margin-left': '3px',
                                    cursor: 'pointer'
                                },
                                on: {
                                    'on-click': (event) => {
                                        this.handleShowSessionInfo(event, {
                                            ...params.row,
                                            connectType: 'TCP'
                                        });
                                    }
                                }
                            })
                        ]);
                    }
                },
                {
                    title: this.nodeType === 'service' ? '服务名' : '接收端',
                    key: 'receiverNodeName',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    hiddenCol: this.nodeType === 'instance',
                    render: (h, params) => {
                        return h('a', {
                            on: {
                                click: () => {
                                    // 下钻参数
                                    this.handleDrillDown(params.row);
                                }
                            }
                        }, '下钻');
                    }
                }
            ];
        },
        // rcm表头
        rcmColumns() {
            return [
                {
                    title: '发送端上下文',
                    key: 'contextName',
                    minWidth: 240,
                    ellipsis: true,
                    render: (h, params) => {
                        const ctx = params.row?.[params?.column?.key];
                        return h('h-button', {
                            attrs: {
                                title: ctx
                            },
                            props: {
                                type: 'text'
                            },
                            style: {
                                width: '100%',
                                padding: 0,
                                'text-align': 'left',
                                overflow: 'hidden',
                                'white-space': 'nowrap',
                                'text-overflow': 'ellipsis'
                            },
                            on: {
                                click: () => {
                                    this.goLinkRcmObserve(ctx);
                                }
                            }
                        }, ctx);
                    }
                },
                {
                    title: '下一个消息编号',
                    key: 'nextMsgNo',
                    ellipsis: true,
                    width: 150
                },
                {
                    title: '当前最大消息号',
                    key: 'toSendMsgNo',
                    ellipsis: true,
                    width: 150
                },
                {
                    title: '上次应答消息编号',
                    key: 'lastAckMsgNo',
                    ellipsis: true,
                    width: 150
                },
                {
                    title: '缓存积压消息数',
                    key: 'sendCacheBacklogMsgSize',
                    width: 150,
                    render: (h, params) => {
                        let sendCacheBacklogMsgSize = '';
                        if (typeof params.row.nextMsgNo === 'number' && typeof params.row.toSendMsgNo === 'number') {
                            sendCacheBacklogMsgSize = params.row.nextMsgNo - params.row.toSendMsgNo;
                        }
                        return h('div', {
                            attrs: {
                                title: sendCacheBacklogMsgSize
                            }
                        }, sendCacheBacklogMsgSize);
                    }
                },
                {
                    title: '网络积压消息数',
                    key: 'sendNetBacklogMsgSize',
                    width: 150,
                    render: (h, params) => {
                        let sendNetBacklogMsgSize = '';
                        if (typeof params.row.toSendMsgNo === 'number' && typeof params.row.lastAckMsgNo === 'number') {
                            sendNetBacklogMsgSize = params.row.toSendMsgNo - params.row.lastAckMsgNo;
                        }
                        return h('div', {
                            attrs: {
                                title: sendNetBacklogMsgSize
                            }
                        }, sendNetBacklogMsgSize);
                    }
                },
                {
                    title: '主题分区',
                    key: 'senderAddr',
                    width: 180,
                    ellipsis: true
                },
                {
                    title: '对端上下文',
                    key: 'targetContextName',
                    minWidth: 240,
                    render: (h, params) => {
                        const ctx = params.row?.[params?.column?.key];
                        return h('h-button', {
                            attrs: {
                                title: ctx
                            },
                            props: {
                                type: 'text'
                            },
                            style: {
                                width: '100%',
                                padding: 0,
                                'text-align': 'left',
                                overflow: 'hidden',
                                'white-space': 'nowrap',
                                'text-overflow': 'ellipsis'
                            },
                            on: {
                                click: () => {
                                    this.goLinkRcmObserve(ctx);
                                }
                            }
                        }, ctx);
                    }
                }
            ];
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            drawerTitle: '',
            columns: [],
            tableData: [],
            rcmList: [],
            tableHeight: 50,
            loading: false
        };
    },
    async mounted() {
        this.fetTableHeight();
        await this.initData();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.hideTipBox);
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.handleDrawerTitle();

            this.loading = true;
            // 判断应用类型
            if (this.nodeType === 'context') {
                this.columns = this.rcmColumns;
                this.rcmList = await this.getRcmConfigList();
                // 获取上下文连接信息
                this.tableData = await this.getRcmTopoConnections();
            } else {
                this.columns = this.insColumns;
                this.tableData = await this.getTopoConnections();
            }
            this.loading = false;
        },

        // 处理弹窗标题内容
        handleDrawerTitle() {
            const { source, target } = this.modalData;
            this.drawerTitle = DRAW_TILE_ENUM[this.nodeType];
            if (source && target) {
                this.drawerTitle = `${this.drawerTitle} (${source} - ${target})`;
            }
        },

        // 信息下钻跳转
        async handleDrillDown(row, type) {
            let arg = {};
            // 跳转的tab名
            const tabName = JUMP_TAB_ENUM?.[this.nodeType];
            // 构建跳转筛选参数
            if (type === 'rcm') {
                arg = {
                    ...row?.drillDownParams,
                    ...row?.rcmDrillDownParams
                };
            } else if (type === 'tcp') {
                arg = {
                    ...row?.drillDownParams,
                    ...row?.tcpDrillDownParams
                };
            } else {
                arg = {
                    ...row?.drillDownParams
                };
            }

            // 跳转至rcm拓扑需提前判断条件
            if (tabName === 'contextTopology') {
                // 上下游关系：判断传递的主题是否存在，不存在则报错提示不进行跳转
                if (arg.topologyView === 'UpstreamDownstream' && !arg?.topics?.length) {
                    return this.$hMessage.warning('下钻主题不存在，不支持跳转查看！');
                }

                const rcmList = await this.getRcmConfigList();
                const rcmIds = rcmList.map(item => {
                    return item?.id;
                });
                // 判断rcm配置实例（rcmId）是否存在，不存在报错提示不进行跳转
                if (!rcmIds.includes(arg.rcmId)) {
                    return this.$hMessage.warning('rcm配置不存在，不支持跳转查看！');
                }
            }

            this.handleDrawerClose();
            this.$hCore.trigger('topo-jump-global-event', {
                tabName, arg, type: 'linkType'
            });
        },

        // 获取rcm配置实例列表
        async getRcmConfigList() {
            const res = await getRcmConfigList({
                productId: this.productId
            });
            return res?.data || [];
        },
        // 展示会话信息
        handleShowSessionInfo(event, row) {
            event.stopPropagation();

            // 调用子组件方法
            const tipBox = this.$refs['tip-box'];
            if (tipBox) {
                tipBox.handleShowTipBox(event, {
                    ...row,
                    clusterId: this.modalData.clusterId,
                    type: this.nodeType
                });
            }
        },
        /**
         *  获取topo连接信息
         *  rcm
         * */
        async getRcmTopoConnections() {
            const { rcmApiList, source, target } = this.modalData;
            let rcmData = [];
            try {
                // 同时获取source->target 和target->source的数据
                const [fromEdge, toEdge] = await Promise.all(rcmApiList);
                const fromData = (fromEdge?.data || []).map(item => {
                    return {
                        ...item,
                        ...item?.target,
                        contextName: source,
                        targetContextName: target
                    };
                });
                const toData = (toEdge?.data || []).map(item => {
                    return {
                        ...item,
                        ...item?.target,
                        contextName: target,
                        targetContextName: source
                    };
                });
                rcmData = fromData.concat(toData);
            } catch (err) {
                console.error(err);
            }
            return rcmData;
        },

        /**
         *  获取topo连接信息
         *  应用类型、集群、应用节点
         * */
        async getTopoConnections() {
            let data = [];
            try {
                const { nodeIds, connectType, nodeStatuses, connectStatus, clusterId, excludeSenderReceiverSame, excludeArbNode } = this.modalData;
                const excludeUnmanagedNode = localStorage.getItem('rcm_excludeUnmanagedNode') ? localStorage.getItem('rcm_excludeUnmanagedNode') === 'false' : true;
                const params = {
                    productId: this.productId,
                    type: this.nodeType,
                    nodeIds: nodeIds || [],
                    ...(connectType && { connectType }),
                    ...(nodeStatuses && { nodeStatuses }),
                    ...(connectStatus && { connectStatus }),
                    ...(clusterId && { clusterId }),
                    ...(excludeArbNode && { excludeArbNode }),
                    excludeUnmanagedNode,
                    excludeSenderReceiverSame: excludeSenderReceiverSame || false
                };
                const res = await getTopoConnections(params);
                if (res.code === '200') {
                    data = res?.data || [];
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }

            return data;
        },

        // 跳转到rcm观测页面
        goLinkRcmObserve(name) {
            const query = {
                rcmId: this.modalData.rcmId,
                rcmName: this.rcmList.find(o => o.id === this.modalData.rcmId)?.name,
                contextName: name
            };
            this.$hCore.navigate('/rcmObservation', { history: true }, query);
        },

        handleDrawerClose() {
            this.modalData.status = false;
        },

        // 自适应表格高度
        fetTableHeight() {
            this.$nextTick(() => {
                const drawerElement = this.$refs['drawer-box']?.$el;
                if (drawerElement) {
                    const targetElement = drawerElement.querySelector('.h-drawer-down');
                    if (targetElement) {
                        // 获取目标元素的高度
                        const targetHeight = targetElement.offsetHeight;
                        // 将高度值存储到 tableHeight 中或其他你需要的位置
                        this.tableHeight = targetHeight - 92;
                    }
                }
            });
        }
    }
};
</script>

<style scoped lang="less">
@import url("@/assets/css/drawer.less");

/deep/ .h-drawer-content {
    border: var(--border);
    background: var(--main-color);
}

/deep/ .h-drawer-body {
    padding-bottom: 10px;
}

/deep/ .h-drawer-header {
    padding: 10px 16px;
    background-color: var(--wrapper-color);
}

/deep/ .h-table th {
    font-size: 12px;
    height: 30px;
    background: #363c4b;
}

/deep/ .h-table td {
    height: 28px;
    background: var(--main-color);
}
</style>
