export default {
    // loopList
    coreRsp: '核心应答',
    coreConfirm: '核心确认',
    tradeConfirm: '交易所确认',
    tradeDeal: '交易所成交',

    // instanceInfoDict
    baseInfo: '基础信息',
    runningInfo: '运行状态',
    extInfo: '扩展信息',
    operateInfo: '节点操作',
    instanceName: '应用节点名',
    instanceDesc: '应用节点类型',
    version: '应用节点版本',
    developPlatform: '应用开发平台',
    ip: '部署服务器',
    port: '管理端口',
    clusterRole: '集群角色',
    collectionType: '集成方式',
    kafkaAddress: 'KafKa地址',
    prometheusAddress: 'Promethus地址',
    kafkaTopicList: 'Topic主题',
    indexName: '时延数据存储索引',
    dataReceivePort: '时延数据接收端口号',
    targetInstanceName: '时延日志输出节点',
    dataDir: '日志输出目录',
    processName: '时延日志文件名关键字',

    // rcmDefaultConfig
    online: '在线',
    offline: '离线'
};
