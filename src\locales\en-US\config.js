export default {
    // loopList
    coreRsp: 'Core Response',
    coreConfirm: 'Core Confirmation',
    tradeConfirm: 'Exchange Confirmation',
    tradeDeal: 'Exchange Deal',

    // instanceInfoDict
    baseInfo: 'Basic Information',
    runningInfo: 'Running Status',
    extInfo: 'Extended Information',
    operateInfo: 'Node Operations',
    instanceName: 'Application Node Name',
    instanceDesc: 'Application Node Type',
    version: 'Application Node Version',
    developPlatform: 'Application Development Platform',
    ip: 'Deployment Server',
    port: 'Management Port',
    clusterRole: 'Cluster Role',
    collectionType: 'Integration Method',
    kafkaAddress: 'Kafka Address',
    prometheusAddress: 'Prometheus Address',
    kafkaTopicList: 'Topic Subject',
    indexName: 'Latency Data Storage Index',
    dataReceivePort: 'Latency Data Receive Port',
    targetInstanceName: 'Latency Log Output Node',
    dataDir: 'Log Output Directory',
    processName: 'Latency Log Filename Keyword',

    // rcmDefaultConfig
    online: 'Online',
    offline: 'Offline'
};
