<template>
    <div>
        <h-drawer
            ref="drawer-box"
            v-model="modalData.status"
            :title="$t('pages.dataSecondAppearance.appearanceResult')"
            width="70"
            :mask-closable="true"
            @on-close="handleDrawerClose">
            <div>
                <a-table
                    :columns="columns"
                    :tableData="tableData"
                    :loading="loading"
                    :hasPage="false"
                    showTitle
                    :height="tableHeight">
                </a-table>
            </div>
        </h-drawer>
    </div>
</template>

<script>
import aTable from '@/components/common/table/aTable';
import importStatusTableIcon from '@/components/common/icon/importStatusTableIcon.vue';
import { getLatestAppearData } from '@/api/brokerApi';
import { LOADING_TYPE_OPTIONS } from '@/components/dataSecondAppearance/constant.js';

export default {
    name: 'AppearanceResultDrawer',
    components: { aTable },
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            // 表格列的配置描述
            columns: [
                {
                    title: this.$t('pages.dataSecondAppearance.tableName'),
                    key: 'tableName',
                    minWidth: 150,
                    ellipsis: true
                },
                {
                    title: this.$t('pages.dataSecondAppearance.cluster'),
                    key: 'clusterName',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: this.$t('pages.dataSecondAppearance.sharding'),
                    key: 'shardingNo',
                    ellipsis: true,
                    minWidth: 90
                },
                {
                    title: this.$t('pages.dataSecondAppearance.loadMode'),
                    minWidth: 100,
                    ellipsis: true,
                    render: (_, params) => {
                        const importMode = LOADING_TYPE_OPTIONS.find(o => o.value === params.row.importMode)?.label || '';
                        return <div class="h-table-cell-ellipsis" title={importMode}>
                            {importMode}
                        </div>;
                    }
                },
                {
                    title: this.$t('pages.dataSecondAppearance.appearanceRule'),
                    key: 'importSql',
                    minWidth: 150,
                    ellipsis: true
                },
                {
                    title: this.$t('pages.dataSecondAppearance.appearanceStatusAndResult'),
                    minWidth: 240,
                    ellipsis: true,
                    render: (_, { row }) => {
                        let iconType, text;
                        switch (row.importStatus) {
                            case 'running':
                                iconType = 'loading';
                                text = this.$t('pages.dataSecondAppearance.running');
                                break;
                            case 'succeeded':
                                iconType = 'success';
                                text = this.$t('pages.dataSecondAppearance.executionStatus') + ` ${row.recordTotalCount}（` +
                                       this.$t('pages.dataSecondAppearance.succeeded') + ` ${row.recordSuccessCount} / ` +
                                       this.$t('pages.dataSecondAppearance.failed') + ` ${row.recordFailCount}）`;
                                break;
                            case 'failed':
                                iconType = 'error';
                                break;
                            case 'pending':
                                iconType = 'offline';
                                text = this.$t('pages.dataSecondAppearance.pending');
                                break;
                        }
                        return <div title={ text } class="h-table-cell-ellipsis">
                            <importStatusTableIcon type={iconType}/>
                            { text }
                            { row.importStatus === 'failed' && row.failInfo
                                ? <h-poptip
                                    autoPlacement
                                    transfer
                                    customTransferClassName='apm-poptip'
                                    content={row.failInfo}
                                    trigger="click">
                                    <span class="click-text hover-underline">{ this.$t('pages.dataSecondAppearance.errorMessage') }</span>
                                </h-poptip>
                                : '' }
                        </div>;
                    }
                }
            ],
            // 表格数据
            tableData: [],
            tableHeight: 0,
            loading: false
        };
    },
    async mounted() {
        this.fetTableHeight();
        await this.initData();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.hideTipBox);
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 初始化数据
        async initData() {
            this.loading = true;
            await this.queryImportDataStatus();
            this.loading = false;
        },
        // 调用接口，查询数据上场状态
        async queryImportDataStatus() {
            const params = {
                productId: this.productId,
                importId: this.modalData.importId
            };
            try {
                const res = await getLatestAppearData(params);
                if (res.code === '200') {
                    // 成功获取数据后更新表格数据
                    this.tableData = res?.data.tables || [];
                } else {
                    this.tableData = [];
                    this.$hMessage.error(res.message);
                }
            } catch (e) {
                this.tableData = [];
            }
        },
        // 处理抽屉关闭事件
        handleDrawerClose() {
            this.modalData.status = false;
        },
        // 自适应设置表格高度
        fetTableHeight() {
            this.tableHeight = this.$refs['drawer-box']?.$el?.offsetTop - (window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 180 : 100);
        }
    }
};
</script>
<style lang="less">
.hover-underline:hover {
    text-decoration: underline;
    text-decoration-color: #2d8de5;
}
</style>

<style scoped lang="less">
@import url("@/assets/css/drawer.less");

/deep/ .h-drawer-content {
    border: var(--border);
    background: var(--main-color);
}

/deep/ .h-drawer-body {
    padding-bottom: 10px;
}

/deep/ .h-drawer-header {
    padding: 10px 16px;
    background-color: var(--wrapper-color);
}

.click-text {
    color: var(--link-color);

    &:hover {
        cursor: pointer;
    }
}
</style>
