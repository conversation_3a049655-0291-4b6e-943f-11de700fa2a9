<template>
    <div>
        <h-msg-box
            v-model="modalInfo.status"
            width="70"
            height="380"
            :escClose="false"
            :mask-closable="false"
            title="链路配置"
            @on-open="handleOpen"
            @on-cancle="cancel"
            >
            <h-form
                ref="formValid1"
                :model="modalData.formItem"
                :rules="timeRule"
                :label-width="100">
                <div class="title">分析目标</div>
                <h-form-item label="选择链路" prop="bizTraceType" required>
                    <h-select
                        v-model="modalData.formItem.bizTraceType"
                        placeholder="请选择链路"
                        :positionFixed="true"
                        :disabled="loading">
                        <h-option v-for="item in bizTraceTypes" :key="item.bizTraceType" :value="item.bizTraceType">{{ item.bizTraceTypeAlias }}</h-option>
                    </h-select>
                </h-form-item>
                <!-- 选择跨度 -->
                <h-form-item label="选择跨度" prop="linkSpanList" required>
                    <h-select
                        v-model="modalData.formItem.linkSpanList"
                        class="line-box"
                        placeholder="选择跨度"
                        :disabled="loading"
                        multiple
                        :multipleNumber="4">
                        <h-option
                            v-for="item in spanList"
                            :key="item"
                            :value="item">
                            {{ item }}</h-option>
                    </h-select>
                </h-form-item>
                <div class="title">数据范围</div>
                <h-row>
                    <h-col span="12">
                        <h-form-item label="统计日期" prop="date" required>
                            <h-date-picker
                                v-model="modalData.formItem.date"
                                type="date"
                                :disabled="loading"
                                placeholder="选择日期"
                                :positionFixed="true"
                                placement="top-start"></h-date-picker>
                        </h-form-item>
                    </h-col>
                    <h-col span="12">
                        <h-form-item label="统计时间" prop="time" required>
                            <h-time-picker
                                v-model="modalData.formItem.time"
                                confirm
                                :disabled="loading"
                                type="timerange"
                                :positionFixed="true"
                                placement="top-start"
                                placeholder="选择时间">
                            </h-time-picker>
                        </h-form-item>
                    </h-col>
                    <h-col span="12">
                        <h-form-item label="统计方式" prop="secondType" required>
                            <h-select
                                v-model="modalData.formItem.secondType"
                                placeholder="请选择统计方式"
                                :disabled="loading"
                                :positionFixed="true"  >
                                <h-option
                                    v-for="item in lineNormList"
                                    :key="item.key"
                                    :value="item.key">{{ item.value }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                    </h-col>
                    <h-col span="12">
                        <h-form-item label="统计口径" prop="interval" required>
                            <h-select
                                v-model="modalData.formItem.interval"
                                placeholder="请选择统计口径"
                                :disabled="loading"
                                :positionFixed="true"  >
                                <h-option v-for="item in intervalList" :key="item.key" :value="item.key">{{ item.value }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                    </h-col>
                </h-row>

                <div class="title">数据过滤</div>
                <h-row>
                    <h-col
                        v-for="item in modalData.bizTags"
                        :key="item.key"
                        span="12">
                        <h-form-item
                            :label="item.label"
                            :labelTitle="item.label"
                            :prop="item.key"
                            :required="item.required">
                            <h-select
                                v-if="item.type === 'select'"
                                v-model="modalData.formItem[item.key]"
                                :disabled="loading"
                                :positionFixed="true">
                                <h-option
                                    v-for="option in item.options"
                                    :key="option.value"
                                    :value="option.value">{{ option.label }}
                                </h-option>
                            </h-select>
                            <h-input
                                v-if="item.type === 'input'"
                                v-model="modalData.formItem[item.key]"
                                :disabled="loading"
                                placeholder="请输入"></h-input>
                        </h-form-item>
                    </h-col>
                </h-row>
            </h-form>

            <template v-slot:footer>
                <a-button type="ghost" @click="cancel">取消</a-button>
                <a-button type="primary" style="margin-left: 8px;" @click="submitForm">确定</a-button>
            </template>
        </h-msg-box>
    </div>

</template>

<script>
import aButton from '@/components/common/button/aButton';
import _ from 'lodash';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        loading: {
            type: Boolean,
            default: false
        },
        bizTraceTypes: {
            type: Array,
            default: []
        },
        traceModels: {
            type: Array,
            default: []
        },
        spanList: {
            type: Array,
            default: []
        }
    },
    data() {
        const timeRule = function (rule, values, callback) {
            if (values.length) {
                let startTime, endTime;
                values.forEach((ele, index) => {
                    const _arr = ele.split(':');
                    if (index) {
                        endTime = (Number(_arr[0])) * 3600 + (Number(_arr[1])) * 60 + (Number(_arr[2]));
                    } else {
                        startTime = (Number(_arr[0])) * 3600 + (Number(_arr[1])) * 60 + (Number(_arr[2]));
                    }
                });
                if ((endTime - startTime) > 18 * 3600) {
                    return callback(new Error('自定义范围不得超过18小时'));
                }
            }
            callback();
        };
        return {
            initData: {},
            modalData: {
                formItem: {},
                bizTags: []
            },
            timeRule: {
                time: [{
                    validator: timeRule, trigger: 'blur'
                }]
            },
            // 线性指标集合
            lineNormList: [
                {
                    key: 'p50',
                    value: '中位数'
                },
                {
                    key: 'avg',
                    value: '平均值'
                }, {
                    key: 'max',
                    value: '最大值'
                }, {
                    key: 'min',
                    value: '最小值'
                }, {
                    key: 'p90',
                    value: '90分位'
                }, {
                    key: 'p95',
                    value: '95分位'
                }, {
                    key: 'p99',
                    value: '99分位'
                }
            ],
            intervalList: [
                {
                    key: 1000,
                    value: '1s'
                },
                {
                    key: 5000,
                    value: '5s'
                },
                {
                    key: 10000,
                    value: '10s'
                },
                {
                    key: 60000,
                    value: '60s'
                }
            ],
            exchangeList: [
                {
                    label: '全部市场',
                    value: '0'
                },
                {
                    label: '深圳交易所',
                    value: 'SZSE'
                },
                {
                    label: '上海交易所',
                    value: 'SSE'
                }
            ],
            // 时间范围
            rangeList: [
                {
                    label: 'ALL_DAY',
                    value: '日盘'
                },
                {
                    label: 'BIDDING',
                    value: '集合竞价'
                },
                {
                    label: 'MORNING',
                    value: '早盘'
                },
                {
                    label: 'AFTERNOON',
                    value: '午盘'
                },
                {
                    label: 'CLOSING-QUOTATION',
                    value: '盘后定价'
                }
            ]
        };
    },
    methods: {
        handleOpen() {
            this.initData = _.cloneDeep(this.modalInfo);
            for (const i in this.initData.formItem) {
                if (this.initData.formItem.hasOwnProperty(i)){
                    this.$set(this.modalData.formItem, i, this.initData.formItem[i]);
                }
            }
            this.modalData.bizTags = this.modalInfo.bizTags;
        },
        submitForm() {
            this.$refs['formValid1'].validate(valid => {
                if (valid) {
                    this.$emit('setInfoData', this.modalData.formItem);
                    this.modalInfo.status = false;
                }
            });
        },
        cancel() {
            this.modalInfo.status = false;
        }
    },
    components: { aButton }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 10px 32px 16px;
}

/deep/ .h-tabs-tabpane {
    padding-right: 4px;
}

.title {
    position: relative;
    padding: 0 0 16px 20px;

    &::before {
        content: "";
        display: inline-block;
        position: absolute;
        left: 0;
        width: 4px;
        height: 17px;
        background: var(--link-color);
    }
}
</style>
