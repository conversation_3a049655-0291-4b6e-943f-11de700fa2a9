<template>
    <div ref="waterfallContainer" class="waterfall-container">
        <draggable-container v-show="preLeft" key="left" v-model="leftTemp" :options="dragOptions" :itemKey="itemKey"
            :containerStyle="getTempColStyle('left')">
            <template #default="{ item }">
                <slot name="item" :item="item" />
            </template>
        </draggable-container>
        <template v-for="(_, columnIndex) of layout">
            <draggable-container :key="columnIndex" v-model="layout[columnIndex]" :options="dragOptions"
                :itemKey="itemKey" :containerStyle="getColStyle(columnIndex)" @start="handleStartDrag"
                @end="handleEndDrag">
                <template #default="{ item }">
                    <slot name="item" :item="item" />
                </template>
            </draggable-container>
        </template>
        <draggable-container v-show="preRight" key="right" v-model="rightTemp" :options="dragOptions" :itemKey="itemKey"
            :containerStyle="getTempColStyle('right')">
            <template #default="{ item }">
                <slot name="item" :item="item" />
            </template>
        </draggable-container>
    </div>
</template>

<script>
import { debounce, throttle, cloneDeep } from 'lodash';
import WaterfallLayoutProps from './waterfallLayoutProps';
import DraggableContainer from './draggableContainer.vue';
import { DragManager } from './dragManager';
import LayoutCacheManager from './layoutCacheManager';

export default {
    name: 'WaterfallLayout',
    components: { DraggableContainer },
    props: WaterfallLayoutProps,
    created() {
        this.layoutCache = new LayoutCacheManager(this.id, this.itemKey); // 缓存管理
        this.dragManager = new DragManager(); // 拖拽管理

        this.historyLayout = []; // 历史布局
        this.unLayoutItems = []; // 除历史布局外的元素
        this.storeLayout = []; // 存储编辑前的视图

        this._destory = false;   // 防止在销毁后触发 resize 回调
        this.currentDrawId = 0; // 绘制任务的标识符，主要用于等高列瀑布流渲染时判断，防止重复绘制
        this.resizeObserver = null; // 视图大小变化监听器
        this.resizeObserverCallback = null;  // 视图大小变化回调方法
    },
    data() {
        return {
            draggable: false, // 是否支持拖拽
            layout: [], // 实际布局
            preLeft: false, // 是否显示左侧新增列
            leftTemp: [], // 左侧新增列数据
            preRight: false, // 是否新增右侧新增列
            rightTemp: [] // 右侧预增列数据
        };
    },
    computed: {
        dragOptions() {
            return {
                group: `draggable-container-${this.id}`,
                disabled: !this.draggable
            };
        },
        gapX() {
            const gapX = Array.isArray(this.gap) ? this.gap[0] : this.gap;
            return `${gapX / 2}px`; // 左右均分边距
        },
        gapY() {
            const gapY = Array.isArray(this.gap) ? this.gap[1] : this.gap;
            return `${gapY}px`;
        },
        commonColStyle() {
            return {
                gap: `${this.gapY}`
            };
        }
    },
    mounted() {
        this.initialize();
    },
    beforeDestroy() {
        this._destory = true;
        this.disconnectObserveResize();
        this.unWatchMouseOver();
    },
    watch: {
        items: {
            deep: true,
            handler() {
                if (this.draggable === false) {
                    // 拖拽模式下，不变化数据
                    this.initialize();
                }
            }
        },
        cols(value) {
            if (typeof value === 'object') {
                this.handleColumnCountChange();
                this.observeResize();
                return;
            }
            this.disconnectObserveResize();
        },
        minWidth() {
            this.handleColumnCountChange();
            this.observeResize();
        }
    },
    methods: {
        /**
         * 开始编辑
         */
        edit() {
            this.draggable = true;
            this.storeLayout = cloneDeep(this.layout);
            this.disconnectObserveResize();
        },

        /**
         * 确认编辑
         */
        okEdit() {
            this.draggable = false;
            this.storeLayout = [];
            if (this.history) {
                this.layoutCache.cache(this.layout);
                this.historyLayout = this.layout;
            }
        },

        /**
         * 取消编辑
         */
        cancelEdit() {
            this.draggable = false;
            this.layout = this.storeLayout;
            this.storeLayout = [];
            this.observeResize();
        },

        /**
         * 恢复布局
         */
        revertLayout() {
            this.layoutCache.clearCache();
            this.initialize();
        },

        /**
         * 获取列样式
         */
        getColStyle(index) {
            const colIndex = this.preLeft ? index + 1 : index;
            const colLength =
                Number(this.preLeft) + this.layout.length + Number(this.preRight);
            return {
                ...this.commonColStyle,
                'padding-left': colIndex !== 0 ? this.gapX : 0,
                'padding-right': colIndex !== colLength - 1 ? this.gapX : 0
            };
        },

        getTempColStyle(position) {
            return {
                ...this.commonColStyle,
                'padding-left': position !== 'left' ? this.gapX : 0,
                'padding-right': position !== 'right' ? this.gapX : 0
            };
        },

        /**
         * 初始化布局
         */
        initialize() {
            this.initializeLayoutData();
            this.draw();
            this.observeResize();
        },

        /**
         * 初始化布局数据
         */
        initializeLayoutData() {
            const cache = this.history ? this.layoutCache.getCache() : [];
            const itemMap = new Map(
                this.items.map((item) => [item[this.itemKey], item])
            );

            const historyLayout = Array.from({ length: cache.length }, () => []);

            for (let colIndex = 0; colIndex < cache.length; colIndex++) {
                for (const key of cache[colIndex]) {
                    if (!itemMap.has(key)) {
                        continue;
                    }
                    historyLayout[colIndex].push(itemMap.get(key));
                    itemMap.delete(key);
                }
            }

            this.historyLayout = historyLayout.filter((col) => !!col.length);
            this.unLayoutItems = [...itemMap.values()];
        },

        /**
         * 开始布局
         * @param items
         * @param layout
         */
        draw() {
            if (!this.canDraw()) {
                return;
            }

            this.currentDrawId++;
            if (this.historyLayout.length) {
                this.layout = this.historyLayout;
            } else {
                const columnCount = this.getColumnCount();
                this.layout = Array.from({ length: columnCount }, () => []);
            }

            if (this.isEqualHeight && !this.draggable) {
                this.insertByShortest(this.currentDrawId, this.unLayoutItems);
            } else {
                this.insertSequentially(this.unLayoutItems);
            }
        },

        canDraw() {
            return this._destory === false && this.$refs.waterfallContainer !== undefined && this.layout !== undefined;
        },

        /**
         * 获取渲染列数
         */
        getColumnCount() {
            const columnCount = this.cols !== undefined
                ? this.getColumnCountByCols()
                : this.minWidth !== undefined
                    ? this.getColumnCountByWidth()
                    : 1;

            if (columnCount <= 0) {
                return 1;
            }

            return Math.min(this.items.length, columnCount);
        },

        /**
         * 根据 minWidth 计算列数
         */
        getColumnCountByWidth() {
            const width =
                this.$refs.waterfallContainer?.clientWidth || 0;
            return Math.floor((width + this.gap) / (this.minWidth + this.gap));
        },

        /**
         * 根据 cols 计算列数
         */
        getColumnCountByCols() {
            if (typeof this.cols === 'number') {
                return this.cols;
            }

            const width = this.$refs.waterfallContainer?.clientWidth || 0;
            const sorted = Object.keys(this.cols)
                .map(Number)
                .sort((a, b) => a - b);

            let columnCount = this.cols[sorted[0]];
            sorted.forEach((bp) => {
                if (width >= bp) {
                    columnCount = this.cols[bp];
                }
            });

            return columnCount;
        },

        /**
         * 按最短列插入
         */
        async insertByShortest(drawId, items) {
            await this.$nextTick(); // 首次计算前，也需等渲染完成后再执行
            if (!this.canInsertByShortest(drawId))  {
                return;
            }

            const columnHeights = this.getInitColumnHeights();
            for (const item of items) {
                const shortestIndex = columnHeights.indexOf(Math.min(...columnHeights));
                this.layout[shortestIndex]?.push(item);

                // 在下一次 DOM 更新后，更新缓存高度
                await this.$nextTick();
                if (!this.canInsertByShortest(drawId))  {
                    return;
                }
                this.updateColumnHeights(columnHeights, shortestIndex);
            }
        },

        canInsertByShortest(drawId) {
            return this.canDraw() && drawId === this.currentDrawId;
        },

        /**
         * 初始化列高度缓存
         */
        getInitColumnHeights() {
            const columnDivs = [...this.$refs.waterfallContainer.children];
            return columnDivs
                .filter((col) => col.style.display !== 'none')
                .map((col) => {
                    const children = [...col.children];
                    return children.reduce(
                        (sum, child) => sum + this.getItemHeight(child),
                        0
                    );
                });
        },

        /**
         * 更新列高度
         */
        updateColumnHeights(columnHeights, columnIndex) {
            const column = [...this.$refs.waterfallContainer.children].filter(
                (col) => col.style.display !== 'none'
            )[columnIndex];

            const lastChild = column?.lastElementChild;
            if (lastChild) {
                columnHeights[columnIndex] += this.getItemHeight(lastChild);
            }
        },

        getItemHeight(item) {
            const style = window.getComputedStyle(item);
            const marginTop = parseFloat(style.marginTop) || 0;
            const marginBottom = parseFloat(style.marginBottom) || 0;
            return item.getBoundingClientRect().height + marginTop + marginBottom;
        },

        /**
         * 顺序插入
         */
        insertSequentially(items) {
            items.forEach((item, index) => {
                const colIndex = index % this.layout.length;
                this.layout[colIndex].push(item);
            });
        },

        /**
         * 监听容器尺寸变化
         */
        observeResize() {
            if (this.resizeObserver !== null || (typeof this.cols !== 'object' && this.minWidth === undefined)) {
                return;
            }

            this.resizeObserverCallback = debounce(this.handleColumnCountChange, 150);
            this.resizeObserver = new ResizeObserver(this.resizeObserverCallback);
            this.resizeObserver.observe(this.$refs.waterfallContainer);
        },

        /**
         * 处理列数变化变化
         */
        handleColumnCountChange() {
            if (this.draggable || this.historyLayout.length !== 0 || this._destory) {
                return;
            }

            const oldColumnCount = this.layout.length;
            const columnCount = this.getColumnCount();

            if (columnCount !== oldColumnCount) {
                this.draw();
            }
        },

        /**
         * 移除监听容器尺寸变化
         */
        disconnectObserveResize() {
            if (this.resizeObserver) {
                this.resizeObserver.disconnect();
                this.resizeObserver = null;
            }

            if (this.resizeObserverCallback) {
                this.resizeObserverCallback.cancel();
                this.resizeObserverCallback = null;
            }
        },

        /**
         * 开始拖拽
         */
        handleStartDrag() {
            const container = this.$refs.waterfallContainer;
            const containerRect = container.getBoundingClientRect();
            this.dragManager.startDrag(containerRect.left, containerRect.width);
            this.watchMouseOver();
        },

        /**
         * 原生 H5 拖拽无法修改拖拽元素的样式，使用 forceFallback 模式
         * 该模式下实际没有 dragover 事件，改用 mouseover 鼠标事件代替
         */
        watchMouseOver() {
            document.addEventListener('mouseover', this.handleContainerDragOver);
        },

        /**
         * 结束拖拽
         */
        async handleEndDrag() {
            this.unWatchMouseOver();
            this.dragManager.endDrag();

            this.preLeft = false;
            this.preRight = false;
            await this.$nextTick();

            this.layout = this.layout.filter((items) => !!items.length);
            if (this.leftTemp.length !== 0) {
                this.layout = [this.leftTemp, ...this.layout];
                this.leftTemp = [];
            }
            if (this.rightTemp.length !== 0) {
                this.layout = [...this.layout, this.rightTemp];
                this.rightTemp = [];
            }
        },

        unWatchMouseOver() {
            document.removeEventListener('mouseover', this.handleContainerDragOver);
        },

        /**
         * 判断是否悬停在视图边缘位置
         */
        handleContainerDragOver: throttle(function (event) {
            if (this.dragManager.draggable === false) {
                return;
            }
            this.doHandleContainerDragOver(event);
        }, 100),

        doHandleContainerDragOver(event) {
            // 是否靠近左侧列
            if (this.dragManager.nearContainerLeft(event)) {
                this.preLeft = true;
            } else {
                this.preLeft = false;
            }

            // 是否靠近右侧列
            if (this.dragManager.nearContainerRight(event)) {
                this.preRight = true;
            } else {
                this.preRight = false;
            }
        }
    }
};
</script>

<style scoped>
.waterfall-container {
    display: flex;
    height: 100%;
}

.draggable-container {
    display: flex;
    flex-direction: column;
    flex-basis: 400px;
    flex-grow: 1;
    min-width: 0;
    height: 100%;
}
</style>
