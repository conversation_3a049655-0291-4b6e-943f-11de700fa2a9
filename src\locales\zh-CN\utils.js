export default {
    // IndexedDB
    clearDatabaseSuccessMessage: '数据库已成功清空',
    clearDatabaseErrorMessage: '清空数据库时发生错误:',

    // pkcs8Rsa
    encryptionFailureMessage: 'RSA 加密失败:',

    // roomClusterNode roomSeviceClusterNode
    totalItems: '全部',
    unknownCluster: '未知集群',
    unknownService: '未知服务',

    // utils
    invalidTimeFormatError: '无效时间格式',
    stringToObjectConversionError: '字符串转对象失败',
    jsonParsingError: 'JSON解析失败',
    abnormalTimeIntervalError: '时间间隔异常: {previousTime} → {currentTime}, 使用默认1秒',
    trillions: '万亿',
    billions: '亿',
    tenThousands: '万',

    // validate
    emptyInputError: '输入不能为空',
    numberInputError: '请输入数字类型',
    integerInputLabel: '请输入整型数字',
    unsignedIntegerRangeError: '超出输入范围，输入范围在-2147483648 ~ 2147483647',
    unsignedIntegerRangeError1: '超出输入范围，输入范围在0 ~ 4294967295',
    unsignedIntegerRangeError2: '超出输入范围，输入范围在-32768 ~ 32767',
    unsignedIntegerRangeError3: '超出输入范围，输入范围在0 ~ 65535',
    unsignedIntegerRangeError4: '超出输入范围，输入范围在-9223372036854775808 ~ 9223372036854775807',
    unsignedIntegerRangeError5: '超出输入范围，输入范围在0 ~ 18446744073709551615',
    unsignedIntegerRangeError6: '所占字节数超出输入范围',
    floatRangeError: '超出输入范围，输入范围为-3.40282e38 ~ 3.40282e38',
    doubleRangeError: '超出输入范围，输入范围为-1.79769e308 ~ 1.79769e308',
    decimalRangeError: '超出输入范围，输入范围为-1.19e4932 ~ 1.19e4932',
    booleanInputLabel: '请输入0或1',
    portNumberInputLabel: '请输入1到65535之间的端口号',
    passwordLabel: '请填写密码',
    charLengthExceedError: '字符长度数不得超过{length}!',
    passwordComplexityDescription: '至少要包含字母 (a~zA~Z)、数字(0~9)、特殊符号(!、$、#、@、*、_)3种字符,长度8~20位',
    numberInputLabel: '请输入数字',
    addressCountDescription: '使用IP:PORT形式，多个地址使用英文逗号分隔',
    booleanTypeError: '布尔类型只支持true、false',
    validArrayFormatLabel: '请输入有效的数组格式',
    validObjectFormatLabel: '请输入有效的对象格式',
    rangeExceedErrorMsg: '{item}重复',
    rangeExceedErrorMsg1: '超出输入范围:',
    rangeExceedErrorMsg2: '超出字段长度：',
    rangeExceedErrorMsg3: '数组长度不能超过{maxLength}个元素',
    rangeExceedErrorMsg4: '数组长度不能少于{minLength}个元素',
    rangeExceedErrorMsg5: '对象属性数量不能超过{maxProperties}个',
    rangeExceedErrorMsg6: '对象属性数量不能超过{minProperties}个',
    rangeExceedErrorMsg7: '缺少必需属性：',
    rangeExceedErrorMsg8: '超出输入范围，输入范围在{minValue} ~ {maxValue}',

    // websocket
    closeConnect: 'ws连接关闭',
    connectSuccess: '连接成功',
    abnormal: 'WS异常',
    connectAbnormal: 'ws连接异常！'
};
