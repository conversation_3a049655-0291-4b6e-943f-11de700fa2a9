import './obsTable.less';
import obsTitle from '@/components/common/title/obsTitle';
export default {
    name: 'obs-table',
    components: { obsTitle },
    props: {
        isSimpleTable: {
            type: Boolean,
            default: false
        },
        hasDarkClass: {
            type: Boolean,
            default: true
        },
        title: {
            type: Object,
            default: () => {}
        },
        subhead: {
            type: String,
            default: ''
        },
        tableData: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: () => []
        },
        render: {
            type: Object,
            default: () => {}
        },
        border: {
            type: Boolean,
            default: false
        },
        canDrag: {
            type: Boolean,
            default: true
        },
        showTitle: {
            type: Boolean,
            default: false
        },
        height: {
            type: Number | String | undefined,
            default: undefined
        },
        width: {
            type: Number | String | undefined,
            default: undefined
        },
        maxHeight: {
            type: Number | String | undefined,
            default: undefined
        },
        minColWidth: {
            type: Number | undefined,
            default: undefined
        },
        loading: {
            type: Boolean,
            default: false
        },
        disabledHover: {
            type: Boolean,
            default: false
        },
        highlightRow: {
            type: Boolean,
            default: false
        },
        immediateRowClick: {
            type: Boolean,
            default: false
        },
        rowSelect: {
            type: Boolean,
            default: false
        },
        rowSelectOnly: {
            type: Boolean,
            default: false
        },
        notSetWidth: {
            type: Boolean,
            default: false
        },
        autoHeadWidth: {
            type: Boolean,
            default: false
        },
        rowClassName: {
            type: Function,
            default: () => {
                return '';
            }
        },
        hasPage: {
            type: Boolean,
            default: false
        },
        showSizer: {
            type: Boolean,
            default: false
        },
        showElevator: {
            type: Boolean,
            default: true
        },
        total: {
            type: Number,
            default: 0
        },
        showTotal: {
            type: Boolean,
            default: true
        },
        simple: {
            type: Boolean,
            default: false
        },
        isBlur: {
            type: Boolean,
            default: false
        },
        noDataText: {
            type: String,
            default() {
                return this.$t('common.noData');
            }
        },
        loadingText: {
            type: String,
            default() {
                return  this.$t('common.loading');
            }
        }
    },
    data() {
        return {
            sortField: '',
            sortType: '',
            page: 1,
            pageSize: 10,
            sizerOption: {
                transfer: true,
                autoPlacement: true
            }
        };
    },
    methods: {
        rowClick(row) {
            this.$emit('rowClick', row);
        },
        onCurrentChange(row){
            this.$emit('on-current-change', row);
        },
        tableSelection(selection){
            this.$emit('selection', selection);
        },
        handleButtonClick(key) {
            this.$emit('button-click', key);
        },
        handleSelectChange(val, key) {
            val && this.$emit('select-change', val, key);
        },
        handleCascaderChange(val, key) {
            this.$emit('cascader-change', val, key);
        },
        handleCheckChange(val, key) {
            this.$emit('check-change', val, key);
        },
        setSelectVal(key, val) {
            this.$refs['obs-title'].setSelectVal(key, val);
        },
        getSelectVal(key) {
            return this.$refs['obs-title'].getSelectVal(key);
        },
        setCheckVals(key, val) {
            this.$refs['obs-title'].setCheckVals(key, val);
        },
        getCheckVals(key) {
            return this.$refs['obs-title'].getCheckVals(key);
        },
        handleInputChange(val, searchKey) {
            this.$emit('input-change', val, searchKey);
        },
        getInputVals() {
            return this.$refs['obs-title'].getInputVals();
        },
        getPageData() {
            return {
                page: this.page,
                pageSize: this.pageSize,
                sortField: this.sortField,
                sortType: this.sortType
            };
        },
        resetPage() {
            this.page = 1;
        },
        resetPageSize() {
            this.pageSize = 10;
        },
        pageChange(page) {
            this.page = page;
            this.$emit('query');
        },
        pageSizeChange(size) {
            this.page = 1;
            this.pageSize = size;
            this.$emit('query');
        },
        sortChange(data) {
            this.sortType = data.order.toUpperCase();
            this.sortField = data.key;
            this.resetPage();
            this.$emit('query');
        },
        resetSortData() {
            this.sortType = '';
            this.sortField = '';
        }
    },
    render() {
        return (
            <div class={ this.hasDarkClass ? 'obs-table' : 'obs-table-default'}>
                {this.title && <obs-title ref = 'obs-title' title={this.title}
                    v-on:button-click={this.handleButtonClick}
                    v-on:select-change={this.handleSelectChange}
                    v-on:cascader-change={this.handleCascaderChange}
                    v-on:check-change={this.handleCheckChange}
                    v-on:input-change={this.handleInputChange}
                >
                    <template slot="extraTitleBox">
                        {this.$slots.extraTitleBox}
                    </template>
                </obs-title>
                }
                {this.subhead && <div class="subhead">{this.subhead}</div>}
                {this.$slots.extraTitle}
                <div class='table-box'>
                    {
                        this.isSimpleTable
                            ? <h-simple-table
                                ref="table"
                                columns={this.columns}
                                data={this.tableData}
                                border={this.border}
                                showTitle={this.showTitle}
                                height={this.height}
                                width={this.width}
                                loading = {this.loading}
                                canDrag={this.canDrag}
                                disabledHover={this.disabledHover}
                                highlightRow={this.highlightRow}
                                immediateRowClick={this.immediateRowClick}
                                rowSelect={this.rowSelect}
                                rowSelectOnly={this.rowSelectOnly}
                                row-class-name={this.rowClassName}
                                no-data-text={this.noDataText}
                                loadingText={this.loadingText}
                                v-on:on-row-click={this.rowClick}
                                v-on:on-sort-change={this.sortChange}
                                v-on:on-current-change={this.onCurrentChange}
                                v-on:on-selection-change={this.tableSelection} />
                            : <h-table
                                ref='obs-table'
                                columns={this.columns}
                                data={this.tableData}
                                border={this.border}
                                showTitle={this.showTitle}
                                height={this.height}
                                width={this.width}
                                canDrag={this.canDrag}
                                maxHeight={this.maxHeight}
                                minColWidth={this.minColWidth}
                                loading={this.loading}
                                disabledHover={this.disabledHover}
                                highlightRow={this.highlightRow}
                                immediateRowClick={this.immediateRowClick}
                                rowSelect={this.rowSelect}
                                rowSelectOnly={this.rowSelectOnly}
                                notSetWidth={this.notSetWidth}
                                autoHeadWidth={this.autoHeadWidth}
                                row-class-name={this.rowClassName}
                                noDataText={this.noDataText}
                                loadingText={this.loadingText}
                                v-on:on-row-click={this.rowClick}
                                v-on:on-sort-change={this.sortChange}
                                v-on:on-current-change={this.onCurrentChange}
                                v-on:on-selection-change={this.tableSelection}
                            ></h-table>
                    }
                    {this.hasPage && !this.loading ? (
                        <h-page
                            isBlur={this.isBlur}
                            total={this.total}
                            current={this.page}
                            pageSize={this.pageSize}
                            v-on:on-change={this.pageChange}
                            v-on:on-page-size-change={this.pageSizeChange}
                            showSizer={this.showSizer}
                            showTotal={this.showTotal}
                            showElevator={this.showElevator}
                            sizerOption={this.sizerOption}
                            simple={this.simple}
                        ></h-page>
                    ) : (
                        ''
                    )}
                </div>
            </div>
        );
    }
};
