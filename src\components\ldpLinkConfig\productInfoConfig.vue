<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <a-title title="产品信息">
                <slot>
                    <p class="slot-text">
                        <span>产品实例：</span>
                        <h-button
                            type="text"
                            class="text-cp"
                            title="点击复制"
                            :data-clipboard-text="productInfo.id"
                            @click="copyToClipboard">
                                {{ productInfo.id }}
                        </h-button>
                    </p>
                </slot>
            </a-title>
            <div class="product-info">
                <p>
                    <span>产品名称：</span>{{ overviewInfo.productName }}
                    <span><h-icon name="t-b-modify" @on-click="updateNameModal(0)"></h-icon></span>
                </p>
                <p>
                    <span>产品类型：</span>{{ productTypeDict[productInfo.productType] || '-' }}
                </p>
                <p>
                    <span>关联业务系统：</span>{{ bizSysTypesConvert || '-' }}
                    <span><h-icon name="t-b-modify" @on-click="updateNameModal(3)"></h-icon></span>
                </p>
                <p>
                    <span>产品配置中心：</span>{{ overviewInfo.configSourceType || '-' }}
                </p>
            </div>
            <!-- 已托管配置信息 -->
            <obs-table
                :title="trusteeshipDeploy.title"
                :tableData="trusteeshipDeploy.tableData"
                :columns="trusteeshipDeploy.columns">
            </obs-table>

            <!-- 汇总总览信息 - RCM产品除外 -->
            <div v-if="productInfo.productType !== 'ldpRcm'" class="info-group">
                <div v-for="(item, index) in tableGroupInfo" :key="index" class="info">
                    <obs-table
                        :title="item.title"
                        :tableData="item.tableData"
                        :columns="item.columns"
                        :height="205"
                        :width="boxWidth / 3" />
                </div>
            </div>
            <!-- RCM 产品汇总信息 -->
            <div v-else class="rcm-info-group">
                <div v-if="rcmInfo.topicStatistics" class="rcm-info">
                    <a-title title="主题"></a-title>
                    <p><span>主题数：</span>
                        {{rcmInfo.topicStatistics.topicNum}}
                    </p>
                    <p><span>主题引用模板：</span>
                        {{rcmInfo.topicStatistics.topicTemplateNum}}
                    </p>
                </div>
                <div v-if="rcmInfo.singletonContextStatistics" class="rcm-info">
                    <a-title title="单例上下文"></a-title>
                    <p><span>单例上下文数：</span>
                        {{rcmInfo.singletonContextStatistics.contextNum}}
                    </p>
                    <p><span>单例上下文引用模板：</span>
                        {{rcmInfo.singletonContextStatistics.contextTemplateNum}}
                    </p>
                </div>
                <div v-if="rcmInfo.clusterContextStatistics" class="rcm-info">
                    <a-title title="集群上下文"></a-title>
                    <p><span>集群上下文数：</span>
                        {{rcmInfo.clusterContextStatistics.contextNum}}
                    </p>
                    <p><span>集群上下文引用模板：</span>
                        {{rcmInfo.clusterContextStatistics.contextTemplateNum}}
                    </p>
                </div>
                <div v-if="rcmInfo.rcmTemplateStatistics" class="rcm-info">
                    <a-title title="模板"></a-title>
                    <p><span>主题模板数：</span>
                        {{rcmInfo.rcmTemplateStatistics.transportTemplateNum}}
                    </p>
                    <p><span>通用上下文模板：</span>
                        {{rcmInfo.rcmTemplateStatistics.singleTenContextTemplateNum}}
                    </p>
                    <p><span>集群上下文模板：</span>
                        {{rcmInfo.rcmTemplateStatistics.clusterContextTemplateNum}}
                    </p>
                </div>
            </div>
        </div>

        <!-- 修改产品信息、仲裁信息弹窗 -->
        <product-manage-name-modal v-if="manageNameInfo.status" :modalInfo="manageNameInfo" @update="reload($event)" />
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { getProductOverview, getProductInstances } from '@/api/productApi';
import { getRcmOverview, getRcmInstanceList } from '@/api/rcmApi';
import Clipboard from 'clipboard';
import aTitle from '@/components/common/title/aTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import productManageNameModal from '@/components/ldpLinkConfig/modal/productManageNameModal.vue';
import { generateTableGroupInfo } from '@/components/ldpLinkConfig/constant.js';
export default {
    name: 'ProductInfoConfig',
    components: { aTitle, obsTable, aLoading, productManageNameModal },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            boxWidth: 100,
            overviewInfo: {},
            manageNameInfo: {
                status: false,
                productName: '',
                id: '',
                type: 0,
                configSourceType: 'zookeeper',
                configInfo: {
                    zkAddr: '',
                    paths: []
                }
            },
            tableGroupInfo: [],
            trusteeshipDeploy: {
                title: {
                    label: '已托管配置'
                },
                columns: [
                    { title: '配置节点名称', key: 'name' },
                    { title: '配置根目录', key: 'path', minWidth: 300 },
                    { title: '配置提供者', key: 'sourceType' },
                    { title: '配置服务地址', key: 'serviceAddress',
                        render: (h, params) => {
                            return h('div', [
                                h('Poptip', {
                                    class: 'apm-poptip',
                                    props: {
                                        title: '配置服务地址',
                                        placement: 'left-end',
                                        positionFixed: true
                                    }
                                }, [
                                    h('Button',
                                        {
                                            props: {
                                                size: 'small',
                                                type: 'text'
                                            }
                                        },
                                        '查看'
                                    ),
                                    h('div', {
                                        slot: 'content'
                                    }, [
                                        params.row.serviceAddress?.split(',')?.map(item => {
                                            return h('p', {
                                                style: {
                                                    padding: '4px 0'
                                                }
                                            }, [
                                                item
                                            ]);
                                        })
                                    ])
                                ])
                            ]);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (h, params) => {
                            return h('div', [
                                h('Button',
                                    {
                                        props: {
                                            size: 'small',
                                            type: 'text'
                                        },
                                        on: {
                                            click: () => {
                                                this.handleConfigJump(params.row);
                                            }
                                        }
                                    },
                                    '配置'
                                )
                            ]);
                        }
                    }
                ],
                tableData: []
            },
            rcmInfo: {
                topicStatistics: {},
                singletonContextStatistics: {},
                clusterContextStatistics: {},
                rcmTemplateStatistics: {}
            },
            loading: false
        };
    },
    mounted() {
        this.resizeObserver = new ResizeObserver(entries => {
            this.fetTableHeight();
        });
        this.resizeObserver.observe(this.$refs['tab-box']);
        this.updateTableGroupInfo();
    },
    beforeDestroy() {
        this.resizeObserver.disconnect(this.$refs['tab-box']);
    },
    methods: {
        updateTableGroupInfo() {
            this.tableGroupInfo = generateTableGroupInfo(this.apmDirDesc);
        },
        // 设置table高度
        fetTableHeight() {
            this.boxWidth = this.$refs['tab-box']?.offsetWidth - 90;
        },
        async initData() {
            this.loading = true;
            this.clearPageData();
            try {
                await this.getProductInstances();
                await this.getProductOverview();
                this.productInfo.productType === 'ldpRcm' && await this.getRcmCountStatistics();
            } finally {
                this.loading = false;
            }
        },
        // 清空页面数据
        clearPageData() {
            this.instances = [];
            this.handlePageData();
        },
        // 获取产品总览信息
        async getProductOverview() {
            const res = await getProductOverview({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.handlePageData(res.data);
            }
        },
        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.instances = res?.data?.instances;
            }
        },
        // 整理渲染页面数据
        handlePageData(data) {
            this.overviewInfo = {
                productName: data?.productName,
                bizSysTypes: data?.bizSysTypes,
                configSourceType: data?.configSourceType,
                configInfo: data?.configInfo
            };
            this.trusteeshipDeploy.tableData = data?.configs || [];
            this.tableGroupInfo[0].tableData = data?.instanceTypes || [];
            this.tableGroupInfo[1].tableData = data?.appClusters || [];
            this.tableGroupInfo[2].tableData = data?.services || [];
            this.tableGroupInfo[3].tableData = data?.hosts || [];
            this.tableGroupInfo[4].tableData = data?.rooms || [];
            this.tableGroupInfo[5].tableData = data?.shardings || [];
        },
        // 展示修改产品节点名称弹窗
        updateNameModal(type) {
            const configInfo = this.overviewInfo?.configInfo || {
                zkAddr: '',
                paths: []
            };
            this.manageNameInfo = {
                status: true,
                id: this.productInfo.id,
                productName: this.overviewInfo.productName,
                productType: this.productInfo.productType,
                type: type,
                bizSysTypes: this.overviewInfo.bizSysTypes,
                configSourceType: this.overviewInfo.configSourceType,
                configInfo: configInfo
            };
        },

        /**
         * 获取RCM的统计信息并合并到当前数据中。
         *
         * 该方法依次执行以下步骤：
         * 1. 调用获取RCM ID列表的异步方法，获取当前RCM的所有ID列表，存储到变量`rcmList`。
         * 2. 调用异步方法获取RCM统计信息，将第一步获取的`rcmList`作为参数传递，接收并存储统计结果到变量`statistics`。
         * 3. 调用合并RCM统计信息的方法，将步骤2中的统计数据合并到现有对象中，简化后续的数据处理。
         */
        async getRcmCountStatistics() {
            try {
                this.initRcmInfo();
                const rcmList = await this.getRcmIdList();
                const statistics = await this.fetchRcmStatistics(rcmList);
                if (statistics && statistics.length > 0) {
                    this.mergeRcmStatistics(statistics);
                }
            } catch (err) {
                console.error(err);
            }
        },
        initRcmInfo() {
            this.rcmInfo = {
                topicStatistics: {},
                singletonContextStatistics: {},
                clusterContextStatistics: {},
                rcmTemplateStatistics: {}
            };
        },
        // 获取rcm配置列表
        async getRcmIdList() {
            let rcmIdList = [];
            const res = await getRcmInstanceList({
                productId: this.productInfo?.id
            });
            if (res.code === '200') {
                rcmIdList = res?.data || [];
            }
            return rcmIdList;
        },
        // 异步获取指定的RCM列表的统计数据
        async fetchRcmStatistics(rcmList) {
            const promises = rcmList.map(element =>
                getRcmOverview({ id: element.id }).catch(() => ({ code: '500', data: null }))
            );
            return Promise.all(promises);
        },
        // 将获取到的RCM统计信息合并到现有统计数据中
        mergeRcmStatistics(values) {
            if (values && values.length > 0) {
                values.forEach(value => {
                    if (value.code === '200' && value.data) {
                        this.updateStatistics('topicStatistics', value.data);
                        this.updateStatistics('singletonContextStatistics', value.data);
                        this.updateStatistics('clusterContextStatistics', value.data);
                        this.updateStatistics('rcmTemplateStatistics', value.data);
                    }
                });
            }
        },
        // 更新特定类别的RCM统计数据
        updateStatistics(type, data) {
            if (!this.rcmInfo[type]) {
                this.rcmInfo[type] = {};
            }
            Object.keys(data[type] || {}).forEach(key => {
                if (!this.rcmInfo[type][key]) {
                    this.rcmInfo[type][key] = 0;
                }
                this.rcmInfo[type][key] += data[type][key];
            });
        },
        // 已托管配置跳转rcm配置
        handleConfigJump(row) {
            this.$hCore.navigate('/rcmDeploy', { history: true }, {
                rcmId: row?.id
            });
        },
        reload(id) {
            this.$emit('reload', id);
        },
        /**
         * 复制指定文本到剪贴板。
         */
        async copyToClipboard() {
            const clipBoard = new Clipboard('.text-cp');
            clipBoard.on('success', (e) => {
                clipBoard.destroy();
                this.$hMessage.success('复制成功');
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        }
    },
    computed: {
        ...mapState({
            apmDirDesc: state => {
                return state.apmDirDesc || {};
            }
        }),
        productTypeDict() {
            return this.apmDirDesc?.productTypeDict || {};
        },
        bizSysTypesConvert() {
            const dicName = `${this.productInfo.productType}BizSysTypeDict`;
            const bizSysEnum = this.apmDirDesc?.[dicName] || {};
            const bizSysTypes = (this.overviewInfo.bizSysTypes || []).map(item => {
                return bizSysEnum[item];
            });
            return bizSysTypes.join(', ');
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/poptip-1.less");

.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 5px);
    cursor: pointer;

    .apm-title {
        margin-top: 5px;
        background: var(--wrapper-color);
    }

    .btn-add {
        position: absolute;
        right: 6px;
        top: -1px;
    }

    .slot-text {
        position: absolute;
        right: 10px;
        top: 0;
        font-size: 12px;
        color: var(--font-opacity-color);

        .text-cp {
            padding: 0;
            color: var(--font-color);

            &:hover {
                color: var(--link-color);
            }
        }
    }

    .product-info {
        background: var(--wrapper-color);
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        width: 100%;
        padding: 0 10px 10px;
        word-wrap: break-word;
        overflow: auto;

        .iconfont {
            color: var(--icon-color);
        }

        .iconfont:hover {
            color: var(--icon-hover);
        }

        .iconfont:active {
            color: var(--icon-press-down);
        }

        & > p {
            color: var(--font-color);
            padding-top: 10px;
            padding-right: 30px;
            line-height: 15px;
            white-space: nowrap;
            word-wrap: break-word;
            overflow: hidden;
            text-overflow: ellipsis;

            & > span {
                padding-left: 10px;
                color: var(--font-opacity-color);
            }
        }
    }

    .rcm-info-group {
        display: flex;
        flex-direction: row;

        .rcm-info {
            flex: 1;
            margin: 5px;
            padding: 0 0 20px;

            & > p {
                color: var(--font-color);
                padding-top: 20px;
                line-height: 15px;

                & > span {
                    padding-left: 13px;
                    color: var(--font-opacity-color);
                }
            }

            & > .apm-title {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .info-group {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(3, 1fr);  //同上

        .info {
            height: 280px;
            margin-left: 15px;
            padding: 0 0 10px;
            border-radius: 5px;
        }

        .info:first-child,
        .info:nth-child(4) {
            margin-left: 0;
        }
    }

    /deep/ .h-poptip-body-content {
        max-height: 150px;
        padding-right: 5px;
    }

    /deep/ .h-poptip-inner {
        background-color: var(--poptip-bg-color);
    }

    /deep/ .h-poptip-title-inner {
        color: #fff;
    }
}
</style>
