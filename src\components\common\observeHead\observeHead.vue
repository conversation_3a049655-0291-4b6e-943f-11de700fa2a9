<template>
    <div class="obs-head">
        <div class="main-top">
            <span v-if="title" class="head-title"> {{ title }}</span>
            <span v-if="hasCurrentTime" class="currenttime">{{ $t('pages.common.todayIsLabel') }}: {{ currentTime }}</span>
            <p v-if="hasProductType">
                {{ $getProductType(productInfo.productType) }}
            </p>
            <h-select v-show="productList.length > 1" v-model="productInstNo" class="securities"
                :placeholder="$t('common.placeholder.select')" :positionFixed="true"
                :clearable="false" @on-change="checkProduct">
                <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName
                    }}</h-option></h-select>
        </div>
        <time-line v-if="hasTimeNode" ref="timeLine" :timeNode="timeNode" :selectedSpan="selectedSpan"
            @selected-span="checkTimeSpan"></time-line>
    </div>
</template>
<script>
import './observeHead.less';
import { formatDate } from '@/utils/utils';
import timeLine from '@/components/ldpProduct/timeLine/timeLine';
import { mapState, mapActions } from 'vuex';
import _ from 'lodash';
export default {
    name: 'ObserveHead',
    components: { timeLine },
    props: {
        title: {
            type: String,
            default: ''
        },
        hasCurrentTime: {
            type: Boolean,
            default: true
        },
        hasProductType: {
            type: Boolean,
            default: true
        },
        hasTimeNode: {
            type: Boolean,
            default: false
        },
        timeNode: {
            type: Array,
            default: () => [
                { startTime: '09:15:00', endTime: '09:30:00', name: '集合竞价' },
                { startTime: '09:30:00', endTime: '11:30:00', name: '早盘竞价交易' },
                { startTime: '11:30:00', endTime: '13:00:00', name: '盘休' },
                { startTime: '13:00:00', endTime: '14:57:00', name: '午盘竞价交易' },
                { startTime: '14:57:00', endTime: '15:00:00', name: '盘后定价' }
            ]
        },
        selectedSpan: {
            type: String,
            default: ''
        }

    },
    data() {
        return {
            currentTime: '',
            productInfo: {},
            productInstNo: ''
        };
    },
    async mounted() {
        await this.init();
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async init() {
            try {
                await this.getProductList();
                const id = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', id])?.productInstNo || this.productList[0].productInstNo;
                this.productInfo = _.find(this.productList, ['productInstNo', this.productInstNo]);
                this.currentTime = formatDate(new Date());
            } catch (e) {
                console.error(e);
            }
        },
        checkProduct(val) {
            this.productInfo = _.find(this.productList, ['productInstNo', val]) || {};
            localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            this.$emit('check-product', val);
        },
        checkTimeSpan(key) {
            this.$emit('selected-span', key);
        },
        changeTimeLineStatus() {
            this.$refs['timeLine'].init();
        }
    }
};
</script>
