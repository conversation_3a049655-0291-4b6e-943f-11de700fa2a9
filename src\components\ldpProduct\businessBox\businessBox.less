.business-box {
    width: 100%;
    height: calc(100% - 23px);
    box-sizing: border-box;
    user-select: none;

    .business-box-title {
        position: relative;
        width: 100%;
        height: 32px;
        margin: 0 auto;
        background: linear-gradient(to right, #202637, #2f3959, #202637);
        background-repeat: no-repeat;
        top: -8px;
        text-align: center;
        line-height: 32px;
        color: var(--font-color);
        font-size: 14px;

        span {
            display: inline-block;
            max-width: calc(100% - 20px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .business-box-title-sign {
            display: inline-block;
            position: absolute;
            top: 9px;
            padding: 0 2px 1px;
            height: 14px; // 设置高度
            line-height: 12px; // 设置行高以实现垂直居中
            margin-left: 4px;
            color: #28314a;
            font-weight: 500;
            font-size: 10px;
            font-family: PingFangSC-Medium;
            background: rgba(255, 255, 255, 0.55);
            border-radius: 2px;
        }
    }

    .h-poptip-rel {
        width: 100%;
    }

    .business-content-list {
        cursor: pointer;

        &::before {
            content: "";
            display: table;
        }

        .business-box-content {
            width: 100%;
            height: auto;
            margin-top: 16px;
            padding: 10px;
            color: var(--font-color);
            font-size: var(--font-size-base);
            background-color: #262d43;

            .business-box-content-header {
                color: #cacfd4;
                height: 24px;
            }

            .business-box-content-header-left {
                display: inline-block;
                max-width: 45%;
                float: left;
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .business-box-content-header-right {
                display: inline-block;
                max-width: 45%;
                float: right;
                text-align: right;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .business-box-content-header-name {
                display: inline-block;
                width: 60%;
                float: left;
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .business-box-content-header-type {
                display: inline-block;
                width: 35%;
                text-align: right;
                float: right;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }

    ul {
        display: inline-grid;
        grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
        grid-auto-rows: row dense;
        grid-gap: 6px;
        width: 100%;
        color: var(--font-color);


        & > .bussiness-btn {
            position: relative;
            text-align: center;
            padding: 0 4px;
            height: 30px;
            line-height: 30px;

            & > .node-type {
                position: absolute;
                left: 0;
                top: 0;
                color: #307a65;
                z-index: 1;
            }

            .h-poptip-rel {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .bussiness-success {
            background-color: #26442d;

            &:hover {
                background-color: #3f6b49;
            }
        }

        .bussiness-warn {
            background-color: #4e3b29;

            &:hover {
                background-color: #755b42;
            }
        }

        .bussiness-error {
            background-color: #4c2132;

            &:hover {
                background-color: #73354d;
            }
        }

        .bussiness-none {
            background-color: #1f3759;

            &:hover {
                background-color: #305180;
            }
        }

        .bussiness-stop {
            background-color: #323952;

            &:hover {
                background-color: #4d5778;
            }
        }

        .main-flag {
            .node-flag("static/mainFlag.png");
        }

        .stand-flag {
            .node-flag("static/standFlag.png");
        }
    }
}


.node-flag(@url) {
    &::after {
        display: inline-block;
        position: absolute;
        left: 1px;
        top: 1px;
        width: 20px;
        height: 20px;
        content: "";
        background: url(@url);
        background-size: 15px 14px;
        background-repeat: no-repeat;
    }
}
