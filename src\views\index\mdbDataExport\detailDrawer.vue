<template>
  <div>
    <h-drawer
      v-model="drawerInfo.status"
      class="table-box"
      :escClose="true"
      :mask-closable="true"
      title="详情"
      width="80"
      height="75"
    >
      <obs-table
        showTitle
        isSimpleTable
        border
        canDrag
        :columns="columns"
        :height="tableHeight"
        :tableData="tableData"
        :loading="tableLoading"
      >
      </obs-table>
    </h-drawer>
  </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import { getLatestExportData } from '@/api/mdbExportApi';
import importStatusTableIcon from '@/components/common/icon/importStatusTableIcon.vue'; // 有用
import { MDB_NO_LOGIN } from '@/config/errorCode';
import { STATUS_COLOR_MAP } from './constant';
import { getCurrentDatetime } from '@/utils/utils';

export default {
    components: {
        obsTable
    },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        productId: {
            type: String
        }
    },
    data() {
        return {
            drawerInfo: this.modalInfo,
            tableData: [],
            tableLoading: false,
            tableHeight: `${window.innerHeight - 100}`,
            timer: null,
            columns: [
                {
                    title: '服务器',
                    key: 'hostName',
                    sortable: true,
                    ellipsis: true
                },
                {
                    title: '节点',
                    key: 'instanceName',
                    sortable: true,
                    ellipsis: true
                },
                {
                    title: '表名',
                    key: 'tableName',
                    sortable: true,
                    ellipsis: true
                },
                {
                    title: '集群',
                    key: 'clusterName',
                    ellipsis: true,
                    sortable: true
                },
                {
                    title: '分片',
                    key: 'shardNo',
                    ellipsis: true,
                    maxWidth: 80,
                    sortable: true
                },
                {
                    title: '远程路径',
                    key: 'remoteExportPath',
                    ellipsis: true
                },
                {
                    title: '导出状态和结果',
                    maxWidth: 140,
                    ellipsis: true,
                    render: (_, { row }) => {
                        let iconType, text;
                        switch (row.status) {
                            case 'exporting':
                                iconType = 'loading';
                                text = '导出中';
                                break;
                            case 'finished':
                                iconType = 'success';
                                text = '导出成功';
                                // text = `记录总数 ${row.recordTotalCount}（成功 ${row.recordSuccessCount} / 失败 ${row.recordFailCount}）`;
                                break;
                            case 'partSuccess':
                                iconType = 'warn';
                                // text = `记录总数 ${row.recordTotalCount}（成功 ${row.recordSuccessCount} / 失败 ${row.recordFailCount}）`;
                                text = '部分成功';
                                break;
                            case 'error':
                                iconType = 'error';
                                text = '导出失败';
                                break;
                            case 'waiting':
                                iconType = 'offline';
                                text = '待导出';
                                break;
                        }
                        return (
                            <div title={text} class="h-table-cell-ellipsis">
                                <importStatusTableIcon type={iconType} />
                                {row.status === 'error' && row.errorMsg ? (
                                    <h-poptip
                                        customTransferClassName="apm-poptip"
                                        transfer
                                        autoPlacement
                                        content={row.errorMsg}
                                        trigger="click"
                                    >
                                        <span style={{ color: '#2d8de5', cursor: 'pointer' }} class="click-text hover-underline">导出失败</span>
                                    </h-poptip>
                                ) : (
                                    text
                                )}
                            </div>
                        );
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 160,
                    render: (_, { row }) => {
                        const canDownload = [
                            STATUS_COLOR_MAP.finished.value
                        ].includes(row.status);
                        return (
                            <span>
                                {
                                    row.loading && <importStatusTableIcon type="loading" />
                                }
                                <a onClick={async () => {
                                    if (!canDownload || row.loading) return;
                                    const key = `${row.tableName}${row.instanceId}`;
                                    this.tableData = this.tableData.map(item => ({
                                        ...item,
                                        loading: key === `${item.tableName}${item.instanceId}`
                                    }));
                                    try {
                                        await this.$emit(
                                            'download',
                                            {
                                                instanceId: row.instanceId,
                                                tableName: row.tableName,
                                                taskId: this.drawerInfo.taskId
                                            },
                                            `${row.tableName}_${getCurrentDatetime()}.zip`
                                        );
                                    } finally {
                                        row.loading = false;
                                        this.tableData = this.tableData.map(item => ({
                                            ...item,
                                            loading: false
                                        }));
                                    }
                                }} class={canDownload && !row.loading ? 'pointer' : 'not-allowed'} style={{ cursor: canDownload && !row.loading ? 'pointer' : 'not-allowed' }}>下载</a>
                            </span>
                        );
                    }
                }
            ]
        };
    },
    mounted() {
        this.initData();
        this.handleSetInterval();
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    },
    methods: {
    /**
     * @description 初始化数据，设置 loading 状态，调用查询数据接口
     */
        async initData() {
            this.tableLoading = true;
            try {
                await this.queryExportData();
            } catch (err) {
                console.error(err);
            } finally {
                this.tableLoading = false;
            }
        },
        handleSetInterval() {
            // 每隔 5s 调用后端接口最近导出状态,并更新 UI 界面
            this.timer = setInterval(async () => {
                await this.queryExportData(true);
            }, 5000);
        },
        /**
     * @description 调用接口，查询导出状态，更新任务详情
     */
        async queryExportData() {
            const params = {
                productId: this.productId,
                taskId: this.modalInfo.taskId
            };
            try {
                const res = await getLatestExportData(params);
                if (res?.code === MDB_NO_LOGIN) {
                    this.$emit('resetLogin');
                    return;
                }
                if (res.code === '200') {
                    const data = res?.data;
                    this.tableData = data?.infos || [];
                }
            } catch (e) {
                console.log('查看历史详情失败 ', e);
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");

.table-box {
    height: 100%;

    /deep/ .h-table-wrapper {
        // height: calc();
    }

    .obs-table {
        background-color: unset;
        height: calc(100% - 10px);
        margin-top: 0;

        /deep/ .table-box {
            height: 100%;
        }
    }

    /deep/ .h-table td {
        background-color: unset;
    }

    .not-allowed {
        color: #969797;

        &:hover {
            cursor: not-allowed;
            color: #969797;
        }
    }
}
</style>
