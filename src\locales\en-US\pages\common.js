export default {
    imageNotDisplayed: 'Image cannot be displayed',
    noData: 'No Data',
    invalidQueryMethod: 'Invalid query method',
    unsupportedComponentError: 'Unsupported component type: {type}, please add this component',
    noMatchingDataDescription: 'No matching data found, try other keywords for search',
    invalidRemoteQueryMethod: 'Invalid remote query method',
    selectLabel: 'Select',
    hideSearch: 'Hide Search',
    expandSearch: 'Expand Search',
    todayIsLabel: 'Today is',
    layoutCacheWarning: 'id, itemKey, layout are empty, cannot store waterfall layout data',

    // dataExportModal and exportHistoryModal
    exportDataLabel: 'Data Export',
    queryFieldName: 'Query Field Name',
    inputAliasPrompt: 'Please enter alias',
    exportFileLabel: 'Export File',
    exportProgress: 'Export Progress',
    exporting: 'Exporting',
    startExport: 'Start Export',
    terminatingLabel: 'Terminating',
    terminateExport: 'Terminate Export',
    exportFailedMessage: 'Export failed!',
    operationSuccess: 'Operation successful',
    operationFailed: 'Operation failed',
    exportFieldConfig: 'Export Field Configuration',
    exportFieldName: 'Export Field Name',
    exportExecutionStatus: 'Export Execution Status',
    downloadLabel: 'Download',
    executionInfo: 'Execution Info',
    exportTimeLabel: 'Export Time',
    exportFileDetail: 'Export File',
    fileSize: 'File Size',
    fileStorageDirectory: 'File Storage Directory',
    spaceOccupied: 'Space Occupied',
    testCaseName: 'Test Case Name',
    caseCreationSuccess: 'Test case created successfully!',
    caseCreationFailed: 'Test case creation failed!',

    // For bestTable, aform, aFormDashboard
    clearDataButton: 'Clear Data',
    quickQuery: 'Quick Query',
    windowHeightWarning: 'Current window height is less than 500, please enlarge the window to view!',
    expandQuery: 'Expand Query',
    exportDataAction: 'Export Data',
    exportHistory: 'Export History',
    loadingLabel: 'Loading',
    selectDateLabel: 'Select Date',
    selectDateTime: 'Select Date Time',
    periodLabel: 'Period',
    averageLabel: 'Average',
    productDelayYesterday: 'Product Delay Yesterday',
    productDelayLastWeek: 'Product Delay Last Week',
    productDelayLastMonth: 'Product Delay Last Month',
    latencyDataSuggestion: 'Latency Data Suggestion (Unit',
    selectTimeLabel: 'Select Time',
    queryResults: 'Query Results',
    addTagButton: 'Add Tag',
    deleteTagButton: 'Delete Tag',
    batchDelete: 'Batch Delete',

    // aFormDashboard
    // aForm
    period: 'Period',
    minValue: 'Min Value',
    averageValue: 'Average Value',
    medianValue: 'Median Value',
    maxValue: 'Max Value',
    paramNotEmptyLabel: 'Parameter cannot be empty',
    enterPositiveIntegerPrompt: 'Please enter a positive integer',
    integerOverflowDescription: 'Input exceeds the maximum safe integer value',
    minValueGreaterMessage: 'Minimum value cannot be greater than maximum value',
    rangeDifferenceNotAllowedMessage: 'Range difference cannot >=1000',
    maxValueSmallerMessage: 'Maximum value cannot be less than minimum value',

    // ainput
    notAllLetters: 'Not all letters',
    inputNotEmptyLabel: 'Input cannot be empty',
    enterKeyword: 'Please enter keyword',
    notIntegerFormat: 'Input is not in integer format',
    notPositiveIntegerFormat: 'Input is not in positive integer format',
    notNegativeIntegerFormat: 'Input is not in negative integer format',
    onlyNumberFormat: 'Only number format allowed',
    onlyNonNegativeIntegerFormat: 'Only non-negative integer format allowed',
    onlyNonPositiveIntegerFormat: 'Only non-positive integer format allowed',
    onlyFloatingNumberFormat: 'Only floating point format allowed',
    onlyPositiveFloatingNumberFormat: 'Only positive floating point format allowed',
    onlyNegativeFloatingNumberFormat: 'Only negative floating point format allowed',
    onlyNonNegativeFloatingNumberFormat: 'Only non-negative floating number format allowed',
    onlyNonPositiveFloatingNumberFormat: 'Only non-positive floating number format allowed',
    notEmailFormat: 'Email address is incorrect',
    onlyColorFormat: 'Only color format allowed',
    onlyUrlFormat: 'Only URL format allowed',
    onlyChineseFormat: 'Only Chinese format allowed',
    onlyACSIIFormat: 'Only ASCII character format allowed',
    invalidPostalCode: 'Postal code format is incorrect, please enter 6-digit code',
    invalidMobileFormat: 'Mobile phone format is incorrect',
    onlyIPv4Format: 'Only IPv4 address format allowed',
    onlyImageFormat: 'Only image format allowed',
    onlyCompressedFileFormat: 'Only compressed file format allowed',
    invalidDateFormat: 'Date format is incorrect',
    invalidQQFormat: 'QQ number format is incorrect',
    invalidPhoneFormat: 'Phone number format is incorrect',
    onlyAlphanumericUnderscore: 'Only strings consisting of numbers, 26 English letters or underscores allowed',
    onlyLetterFormat: 'Only letter format allowed',
    onlyUppercaseFormat: 'Only uppercase letter format allowed',
    onlyLowercaseFormat: 'Only lowercase letter format allowed',

    // ObserveHead
    noDataAvailable: 'No Data',
    auctionCollection: 'Auction Collection',
    morningTradeAuction: 'Morning Trade Auction',
    marketBreak: 'Market Break',
    afternoonTradeAuction: 'Afternoon Trade Auction',
    closeOfDayPricing: 'Close of Day Pricing'

    // topo  ldpNodeTopo
};
