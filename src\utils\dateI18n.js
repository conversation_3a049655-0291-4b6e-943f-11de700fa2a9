/**
 * 国际化日期时间格式化工具
 * 根据当前语言类型调用不同的时间、日期格式化方法
 */

/**
 * 获取当前语言类型
 * @returns {string} 当前语言代码 ('zh-CN' | 'en-US')
 */
export function getCurrentLocale() {
    // 优先从 Vue 实例获取
    if (typeof window !== 'undefined' && window.Vue && window.Vue.prototype.$i18n) {
        return window.Vue.prototype.$i18n.locale;
    }

    // 从本地存储获取
    if (typeof localStorage !== 'undefined') {
        return localStorage.getItem('apm-language') || 'zh-CN';
    }

    // 默认返回中文
    return 'zh-CN';
}

/**
 * 根据语言类型获取日期格式配置
 * @param {string} locale 语言代码
 * @returns {object} 日期格式配置
 */
export function getDateFormats(locale = getCurrentLocale()) {
    const formats = {
        'zh-CN': {
            date: 'YYYY-MM-DD',
            time: 'HH:mm:ss',
            datetime: 'YYYY-MM-DD HH:mm:ss',
            dateShort: 'MM-DD',
            timeShort: 'HH:mm',
            monthDay: 'MM月DD日',
            yearMonth: 'YYYY年MM月',
            weekday: ['日', '一', '二', '三', '四', '五', '六'],
            months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            ampm: ['上午', '下午']
        },
        'en-US': {
            date: 'MM/DD/YYYY',
            time: 'HH:mm:ss',
            datetime: 'MM/DD/YYYY HH:mm:ss',
            dateShort: 'MM/DD',
            timeShort: 'HH:mm',
            monthDay: 'MMM DD',
            yearMonth: 'MMM YYYY',
            weekday: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
            months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            ampm: ['AM', 'PM']
        }
    };

    return formats[locale] || formats['zh-CN'];
}

/**
 * 通用日期格式化函数
 * @param {string|number|Date} date 日期
 * @param {string} format 格式字符串
 * @param {string} locale 语言代码
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format, locale = getCurrentLocale()) {
    if (!date) return '';

    const d = new Date(date);
    if (d.toString().toLowerCase() === 'invalid date') return '';

    const formats = getDateFormats(locale);
    const actualFormat = format || formats.datetime;

    const pad = n => n.toString().padStart(2, '0');

    const map = {
        YYYY: d.getFullYear(),
        MM: pad(d.getMonth() + 1),
        DD: pad(d.getDate()),
        HH: pad(d.getHours()),
        mm: pad(d.getMinutes()),
        ss: pad(d.getSeconds()),
        MMM: formats.months[d.getMonth()],
        weekday: formats.weekday[d.getDay()]
    };

    return actualFormat.replace(/YYYY|MMM|MM|DD|HH|mm|ss|weekday/g, match => map[match]);
}

/**
 * 根据语言格式化日期（仅日期部分）
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 格式化后的日期
 */
export function formatDateOnly(date, locale = getCurrentLocale()) {
    const formats = getDateFormats(locale);
    return formatDate(date, formats.date, locale);
}

/**
 * 根据语言格式化时间（仅时间部分）
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 格式化后的时间
 */
export function formatTimeOnly(date, locale = getCurrentLocale()) {
    const formats = getDateFormats(locale);
    return formatDate(date, formats.time, locale);
}

/**
 * 根据语言格式化日期时间
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(date, locale = getCurrentLocale()) {
    const formats = getDateFormats(locale);
    return formatDate(date, formats.datetime, locale);
}

/**
 * 根据语言格式化短日期（月-日）
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 格式化后的短日期
 */
export function formatDateShort(date, locale = getCurrentLocale()) {
    const formats = getDateFormats(locale);
    return formatDate(date, formats.dateShort, locale);
}

/**
 * 根据语言格式化短时间（时:分）
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 格式化后的短时间
 */
export function formatTimeShort(date, locale = getCurrentLocale()) {
    const formats = getDateFormats(locale);
    return formatDate(date, formats.timeShort, locale);
}

/**
 * 根据语言格式化月日
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 格式化后的月日
 */
export function formatMonthDay(date, locale = getCurrentLocale()) {
    const formats = getDateFormats(locale);
    return formatDate(date, formats.monthDay, locale);
}

/**
 * 根据语言格式化年月
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 格式化后的年月
 */
export function formatYearMonth(date, locale = getCurrentLocale()) {
    const formats = getDateFormats(locale);
    return formatDate(date, formats.yearMonth, locale);
}

/**
 * 根据语言获取星期几
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 星期几
 */
export function getWeekday(date, locale = getCurrentLocale()) {
    if (!date) return '';

    const d = new Date(date);
    if (d.toString().toLowerCase() === 'invalid date') return '';

    const formats = getDateFormats(locale);
    return formats.weekday[d.getDay()];
}

/**
 * 相对时间格式化（多语言支持）
 * @param {number} timestamp 时间戳（秒）
 * @param {string} locale 语言代码
 * @returns {string} 相对时间描述
 */
export function formatTimeAgo(timestamp, locale = getCurrentLocale()) {
    if (!timestamp) return '0';

    const currentTime = Math.floor(Date.now() / 1000);
    const timeDifference = Math.max(currentTime - timestamp, 0);

    const timeUnits = {
        'zh-CN': {
            second: '秒前',
            seconds: '秒前',
            minute: '分钟前',
            minutes: '分钟前',
            hour: '小时前',
            hours: '小时前',
            day: '天前',
            days: '天前'
        },
        'en-US': {
            second: 'second ago',
            seconds: 'seconds ago',
            minute: 'minute ago',
            minutes: 'minutes ago',
            hour: 'hour ago',
            hours: 'hours ago',
            day: 'day ago',
            days: 'days ago'
        }
    };

    const units = timeUnits[locale] || timeUnits['zh-CN'];

    if (timeDifference < 60) {
        return locale === 'zh-CN'
            ? `${timeDifference}${units.seconds}`
            : `${timeDifference} ${timeDifference <= 1 ? units.second : units.seconds}`;
    } else if (timeDifference < 3600) {
        const minutes = Math.floor(timeDifference / 60);
        return locale === 'zh-CN'
            ? `${minutes}${units.minutes}`
            : `${minutes} ${minutes <= 1 ? units.minute : units.minutes}`;
    } else if (timeDifference < 86400) {
        const hours = Math.floor(timeDifference / 3600);
        return locale === 'zh-CN'
            ? `${hours}${units.hours}`
            : `${hours} ${hours <= 1 ? units.hour : units.hours}`;
    } else {
        const days = Math.floor(timeDifference / 86400);
        return locale === 'zh-CN'
            ? `${days}${units.days}`
            : `${days} ${days <= 1 ? units.day : units.days}`;
    }
}

/**
 * 智能日期格式化（根据时间距离选择合适的格式）
 * @param {string|number|Date} date 日期
 * @param {string} locale 语言代码
 * @returns {string} 智能格式化后的日期
 */
export function formatDateSmart(date, locale = getCurrentLocale()) {
    if (!date) return '';

    const d = new Date(date);
    if (d.toString().toLowerCase() === 'invalid date') return '';

    const now = new Date();
    const diffTime = now.getTime() - d.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    // 今天
    if (diffDays === 0) {
        return locale === 'zh-CN' ? '今天' : 'Today';
    }
    // 昨天
    else if (diffDays === 1) {
        return locale === 'zh-CN' ? '昨天' : 'Yesterday';
    }
    // 一周内
    else if (diffDays < 7) {
        return getWeekday(date, locale);
    }
    // 超过一周
    else {
        return formatDateOnly(date, locale);
    }
}

/**
 * 创建 Vue 混入，提供国际化日期格式化方法
 */
export const dateI18nMixin = {
    methods: {
        // 获取当前语言
        $getCurrentLocale: getCurrentLocale,

        // 格式化日期时间
        $formatDate: formatDate,
        $formatDateOnly: formatDateOnly,
        $formatTimeOnly: formatTimeOnly,
        $formatDateTime: formatDateTime,
        $formatDateShort: formatDateShort,
        $formatTimeShort: formatTimeShort,
        $formatMonthDay: formatMonthDay,
        $formatYearMonth: formatYearMonth,

        // 其他格式化方法
        $getWeekday: getWeekday,
        $formatTimeAgo: formatTimeAgo,
        $formatDateSmart: formatDateSmart
    }
};

// 默认导出所有方法
export default {
    getCurrentLocale,
    getDateFormats,
    formatDate,
    formatDateOnly,
    formatTimeOnly,
    formatDateTime,
    formatDateShort,
    formatTimeShort,
    formatMonthDay,
    formatYearMonth,
    getWeekday,
    formatTimeAgo,
    formatDateSmart,
    dateI18nMixin
};
