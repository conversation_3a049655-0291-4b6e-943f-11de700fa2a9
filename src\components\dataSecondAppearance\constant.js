import i18n from '@/locales/i18n';

// 加载方式选项
export const LOADING_TYPE_OPTIONS = [
    {
        value: 1,
        label: i18n.t('pages.dataSecondAppearance.append')
    },
    {
        value: 2,
        label: i18n.t('pages.dataSecondAppearance.overwrite')
    },
    {
        value: 3,
        label: i18n.t('pages.dataSecondAppearance.appendAndOverwrite')
    }
];

// 执行状态选项
export const EXECUTE_STATUS_OPTIONS = [
    {
        value: 'pending',
        label: i18n.t('pages.dataSecondAppearance.pending')
    },
    {
        value: 'running',
        label: i18n.t('pages.dataSecondAppearance.running')
    },
    {
        value: 'succeeded',
        label: i18n.t('pages.dataSecondAppearance.succeeded')
    },
    {
        value: 'failed',
        label: i18n.t('pages.dataSecondAppearance.failed')
    }
];

export const STATUS_COLOR_MAP = {
    pending: {
        color: '#999999',
        text: i18n.t('pages.dataSecondAppearance.pending')
    },
    running: {
        color: '#2D8DE5',
        text: i18n.t('pages.dataSecondAppearance.running')
    },
    succeeded: {
        color: '#4ECA89',
        text: i18n.t('pages.dataSecondAppearance.succeeded')
    },
    failed: {
        color: '#F5222D',
        text: i18n.t('pages.dataSecondAppearance.failed')
    }
};

// 上场任务执行状态枚举
export const TASK_EXECUTE_STATUS = [
    {
        value: 'warn',
        label: i18n.t('pages.dataSecondAppearance.warn')
    },
    {
        value: 'succeeded',
        label: i18n.t('pages.dataSecondAppearance.success')
    },
    {
        value: 'failed',
        label: i18n.t('pages.dataSecondAppearance.error')
    }
];

// 创建二次上场任务步骤
export const CREATE_TASK_TIPS = {
    0: {
        content: i18n.t('pages.dataSecondAppearance.selectTableTip')
    },
    1: {
        content: i18n.t('pages.dataSecondAppearance.confirmRuleTip')
    },
    2: {
        content: i18n.t('pages.dataSecondAppearance.reviewTip')
    }
};
